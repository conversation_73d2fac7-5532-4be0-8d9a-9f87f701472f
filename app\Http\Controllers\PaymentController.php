<?php

namespace App\Http\Controllers;

use App\Events\PaymentRecieved;
use App\Mail\OneTimePaymentReceiptMail;
use App\Models\Donor;
use App\Models\User;
use App\Models\Transaction;
use App\Services\PaymentService;
use App\Services\FailedTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Stripe\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Stripe\PaymentIntent;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\Exception\AuthenticationException;
use Stripe\Exception\ApiConnectionException;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\RateLimitException;
use Exception;
use App\Models\FailedTransaction;


class PaymentController extends Controller
{
    protected $paymentService;
    protected $failedTransactionService;

    public function __construct(PaymentService $paymentService, FailedTransactionService $failedTransactionService)
    {
        $this->paymentService = $paymentService;
        $this->failedTransactionService = $failedTransactionService;
    }

    /**
     * Create a payment intent for a one-time donation
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPaymentIntent(Request $request)
    {
        try {
            $request->merge([
                'anonymousToPublic' => $request->has('anonymousToPublic'),
                'completeAnonymity' => $request->has('completeAnonymity'),
            ]);

            $validated = $request->validate([
                'firstName' => 'required|string|max:255',
                'lastName' => 'nullable|string|max:255',
                'email' => 'required|email',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'address' => 'required|string|max:255',
                'message' => 'nullable|string|max:255',
                'anonymousToPublic' => 'nullable|boolean',
                'completeAnonymity' => 'nullable|boolean',
                'amount' => 'required|numeric|min:1',
            ]);

            $paymentIntent = PaymentIntent::create([
                'amount' => $validated['amount'] * 100,
                'currency' => 'usd',
                'receipt_email' => $validated['email'],
                'metadata' => [
                    'first_name' => $validated['firstName'] ?? null,
                    'last_name' => $validated['lastName'] ?? null,
                    'email' => $validated['email'],
                    'address' => $validated['address'] ?? null,
                    'message' => $validated['donationMessage'] ?? null,
                    'anonymous_to_public' => $validated['anonymousToPublic'] ?? false,
                    'anonymous_to_all' => $validated['completeAnonymity'] ?? false,
                    'city' => $validated['city'],
                    'state' => $validated['state'],
                    'paymentFrequency' => 'one-time',
                ]
            ]);

            return response()->json([
                'clientSecret' => $paymentIntent->client_secret,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation error',
                'messages' => $e->errors()
            ], 422);
        } catch (CardException|InvalidRequestException|AuthenticationException|ApiConnectionException|RateLimitException|ApiErrorException $e) {
            // Record failed payment intent creation
            $this->failedTransactionService->recordIntentCreationFailure($request, $e, [
                'donatedTo' => $request->donatedTo ?? null,
            ]);

            Log::error('Payment intent creation failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Payment processing error',
                'message' => $this->getUserFriendlyErrorMessage($e),
            ], 500);
        } catch (\Exception $e) {
            // Record failed payment intent creation for unknown errors
            $this->failedTransactionService->recordIntentCreationFailure($request, $e, [
                'donatedTo' => $request->donatedTo ?? null,
                'error_type' => 'unknown_exception',
            ]);

            Log::error('Payment intent creation failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Payment processing error',
                'message' => 'Unable to process payment. Please try again later.'
            ], 500);
        }
    }

    /**
     * Store a completed transaction for a one-time donation
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeTransaction(Request $request)
    {
        try {
            $request->merge([
                'anonymousToPublic' => $request->has('anonymousToPublic'),
                'completeAnonymity' => $request->has('completeAnonymity'),
            ]);

            $data = $request->validate([
                'amount' => 'required|numeric|min:1',
                'donatedTo' => 'required|string',
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email',
                'address' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'message' => 'nullable|string|max:255',
                'anonymousToPublic' => 'nullable|boolean',
                'completeAnonymity' => 'nullable|boolean',
                'paymentIntentId' => 'required|string'
            ]);

            $user = User::where('slug', $data['donatedTo'])->firstOrFail();

            DB::beginTransaction();

            if ($data['completeAnonymity']) {
                $data['anonymousToPublic'] = true;
            }

            $donor = Donor::create([
                'name' => trim(($data['firstName'] ?? '') . ' ' . ($data['lastName'] ?? '')),
                'email' => $data['email'],
                'address' => $data['address'],
                'city' => $data['city'],
                'state' => $data['state'],
                'message_for_fundraiser' => $data['message'] ?? null,
                'amount_donate' => $data['amount'],
                'anonymous_for_public' => $data['anonymousToPublic'] ?? false,
                'anonymous_for_all' => $data['completeAnonymity'] ?? false,
                'payment_frequency' => 'one-time',
            ]);

            DB::table('user_donors')->insert([
                'user_id' => $user->id,
                'donor_id' => $donor->id,
                'amount' => $data['amount'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Retrieve the payment intent from Stripe to get the payment method
            $paymentIntent = PaymentIntent::retrieve($data['paymentIntentId']);
            $paymentMethod = $paymentIntent->payment_method_types[0]; // Dynamically fetch the payment method

            // Store transaction with dynamically fetched payment method
            Transaction::create([
                'payment_intent_id' => $data['paymentIntentId'],
                'amount' => $data['amount'],
                'currency' => 'usd',
                'status' => 'success',
                'payment_method' => $paymentMethod,
                'description' => "Donation to $user->name",
                'is_refunded' => false,
                'refund_amount' => 0,
                'refund_id' => null,
                'refunded_at' => null,
            ]);

            DB::commit();

            Event::dispatch(new PaymentRecieved((float) $data['amount'], $donor));

            return response()->json([
                'success' => true,
                'message' => 'Transaction stored successfully.',
                'redirect_url' => route('successPage'),
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation error',
                'messages' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            // Record failed database storage
            $this->failedTransactionService->recordDatabaseStorageFailure(
                $request->paymentIntentId ?? 'unknown',
                $request,
                $e,
                [
                    'donatedTo' => $request->donatedTo ?? null,
                    'stage' => 'transaction_storage',
                ]
            );

            Log::error('Transaction storage failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Transaction error',
                'message' => 'Unable to store transaction. Please contact support.'
            ], 500);
        }
    }

    /**
     * Create a payment intent for organization one-time donation
     */
    public function createPaymentIntentforOrgOneTime(Request $request)
    {
        try {
            $amount = $request->amount * 100;

            $intent = PaymentIntent::create([
                'amount' => $amount,
                'currency' => 'usd',
                'automatic_payment_methods' => ['enabled' => true],
                'receipt_email' => $request->email,
            ]);

            return response()->json(['clientSecret' => $intent->client_secret]);
        } catch (CardException|InvalidRequestException|AuthenticationException|ApiConnectionException|RateLimitException|ApiErrorException $e) {
            // Record failed payment intent creation
            $this->failedTransactionService->recordIntentCreationFailure($request, $e, [
                'donation_type' => 'organization_one_time',
            ]);

            Log::error('Organization payment intent creation failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Payment processing error',
                'message' => $this->getUserFriendlyErrorMessage($e),
            ], 500);
        } catch (\Exception $e) {
            // Record failed payment intent creation for unknown errors
            $this->failedTransactionService->recordIntentCreationFailure($request, $e, [
                'donation_type' => 'organization_one_time',
                'error_type' => 'unknown_exception',
            ]);

            Log::error('Organization payment intent creation failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Payment processing error',
                'message' => 'Unable to process payment. Please try again later.'
            ], 500);
        }
    }

    /**
     * Store a completed transaction for an organization one-time donation
     */
    public function storeTransactionForOrgOneTime(Request $request)
    {
        try {
            $request->merge([
                'anonymousToPublic' => $request->has('anonymousToPublic'),
                'completeAnonymity' => $request->has('completeAnonymity'),
            ]);

            $data = $request->validate([
                'amount' => 'required|numeric|min:1',
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email',
                'message' => 'nullable|string|max:255',
                'address' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'anonymousToPublic' => 'nullable|boolean',
                'completeAnonymity' => 'nullable|boolean',
                'paymentIntentId' => 'required|string',
                'paymentFrequency' => 'required|in:oneTime,daily,monthly,quarterly,annually',
            ]);

            DB::beginTransaction();

            if ($data['completeAnonymity']) {
                $data['anonymousToPublic'] = true;
            }

            $donorData = [
                'name' => trim(($data['firstName'] ?? '') . ' ' . ($data['lastName'] ?? '')),
                'email' => $data['email'],
                'address' => $data['address'],
                'city' => $data['city'],
                'state' => $data['state'],
                'message_for_fundraiser' => $data['message'],
                'amount_donate' => $data['amount'],
                'payment_frequency' => $data['paymentFrequency'],
                "anonymous_for_public" => $data['anonymousToPublic'] ?? false,
                "anonymous_for_all" => $data['completeAnonymity'] ?? false,
            ];
            $donor = Donor::create($donorData);

            $paymentIntent = PaymentIntent::retrieve($data['paymentIntentId']);
            $paymentMethod = $paymentIntent->payment_method_types[0];

            Transaction::create([
                'payment_intent_id' => $data['paymentIntentId'],
                'amount' => $data['amount'],
                'currency' => 'usd',
                'status' => 'success',
                'payment_method' => $paymentMethod,
                'description' => "Donated to Organization",
                'is_refunded' => false,
                'refund_amount' => 0,
                'refund_id' => null,
                'refunded_at' => null,
            ]);

            DB::commit();

            Event::dispatch(new PaymentRecieved((float) $data['amount'], $donor));

            return response()->json([
                'success' => true,
                'message' => 'Transaction stored successfully.',
                'redirect_url' => route('successPage'),
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation error',
                'messages' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            // Record failed database storage
            $this->failedTransactionService->recordDatabaseStorageFailure(
                $request->paymentIntentId ?? 'unknown',
                $request,
                $e,
                [
                    'donation_type' => 'organization_one_time',
                    'stage' => 'transaction_storage',
                ]
            );

            Log::error('Organization transaction storage failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Transaction error',
                'message' => 'Unable to store transaction. Please contact support.'
            ], 500);
        }
    }

    /**
     * Create subscription for organization
     */
    public function createSubscriptionForOrg(Request $request)
    {
        \Stripe\Stripe::setApiKey(config('services.stripe.secret'));

        $request->validate([
            'email' => 'required|email',
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'amount' => 'required|numeric|min:1',
            'paymentFrequency' => 'required|in:daily,monthly,quarterly,annually',
        ]);

        try {
            $customer = \Stripe\Customer::create([
                'email' => $request->email,
                'name' => $request->firstName . ' ' . $request->lastName,
            ]);

            // Convert amount to cents
            $amountInCents = $request->amount * 100;

            // Determine frequency
            $interval = match ($request->paymentFrequency) {
                'daily' => 'day',
                'monthly' => 'month',
                'quarterly' => 'month',
                'annually' => 'year',
            };

            $intervalCount = $request->paymentFrequency === 'quarterly' ? 3 : 1;

            $price = \Stripe\Price::create([
                'unit_amount' => $amountInCents,
                'currency' => 'usd',
                'recurring' => [
                    'interval' => $interval,
                    'interval_count' => $intervalCount,
                ],
                'product_data' => [
                    'name' => 'Recurring Donation',
                ],
            ]);

            // Create a payment intent for the initial payment
            $paymentIntent = PaymentIntent::create([
                'amount' => $amountInCents,
                'currency' => 'usd',
                'customer' => $customer->id,
                'receipt_email' => $customer->email,
                'setup_future_usage' => 'off_session',
                'description' => 'Initial payment for subscription',
                'metadata' => [
                    'subscription_type' => $request->paymentFrequency,
                    'price_id' => $price->id
                ]
            ]);

            return response()->json([
                'clientSecret' => $paymentIntent->client_secret,
                'customerId' => $customer->id,
                'priceId' => $price->id,
                'paymentIntentId' => $paymentIntent->id,
            ]);
        } catch (CardException|InvalidRequestException|AuthenticationException|ApiConnectionException|RateLimitException|ApiErrorException $e) {
            // Record failed subscription creation
            $this->failedTransactionService->recordSubscriptionCreationFailure($request, $e, [
                'donation_type' => 'organization_subscription',
            ]);

            Log::error('Subscription creation failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Subscription creation failed',
                'message' => $this->getUserFriendlyErrorMessage($e),
            ], 500);
        } catch (\Exception $e) {
            // Record failed subscription creation for unknown errors
            $this->failedTransactionService->recordSubscriptionCreationFailure($request, $e, [
                'donation_type' => 'organization_subscription',
                'error_type' => 'unknown_exception',
            ]);

            Log::error('Subscription creation failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Subscription creation failed',
                'message' => 'Unable to create subscription. Please try again later.'
            ], 500);
        }
    }

    /**
     * Confirm subscription
     */
    public function confirmSubscription(Request $request)
    {
        \Stripe\Stripe::setApiKey(config('services.stripe.secret'));

        $request->validate([
            'paymentMethodId' => 'required|string',
            'priceId' => 'required|string',
            'customerId' => 'required|string',
            'paymentIntentId' => 'required|string',
            'amount' => 'required|numeric',
            'paymentFrequency' => 'required|string',
        ]);

        DB::beginTransaction();

        try {
            // Get the payment method object
            $paymentMethod = \Stripe\PaymentMethod::retrieve($request->paymentMethodId);

            // Attach payment method to customer
            $paymentMethod->attach(['customer' => $request->customerId]);

            // Set it as the default payment method
            \Stripe\Customer::update(
                $request->customerId,
                ['invoice_settings' => ['default_payment_method' => $request->paymentMethodId]]
            );

            // Calculate next payment date based on frequency
            $nextPaymentDate = match ($request->paymentFrequency) {
                'daily' => now()->addDay(),
                'monthly' => now()->addMonth(),
                'quarterly' => now()->addMonths(3),
                'annually' => now()->addYear(),
                default => now()->addMonth(),
            };

            // Create the subscription WITHOUT immediate charge since payment was already processed
            $subscription = \Stripe\Subscription::create([
                'customer' => $request->customerId,
                'items' => [['price' => $request->priceId]],
                'default_payment_method' => $request->paymentMethodId,
                'billing_cycle_anchor' => $nextPaymentDate->timestamp, // Set next billing cycle
                'proration_behavior' => 'none', // Don't prorate
            ]);

            // Create the subscription record directly with the Stripe subscription ID
            $subscriptionId = DB::table('subscriptions')->insertGetId([
                'subscription_id' => $subscription->id,
                'customer_id' => $request->customerId,
                'payment_frequency' => $request->paymentFrequency,
                'status' => 'active',
                'amount' => $request->amount,
                'start_date' => now(),
                'next_payment_at' => $nextPaymentDate,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Subscription confirmed and created successfully.',
                'subscription' => $subscription->id,
                'subscriptionId' => $subscriptionId,
                'paymentStatus' => 'succeeded', // Payment was already processed
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            // Record failed subscription confirmation
            $this->failedTransactionService->recordSubscriptionCreationFailure($request, $e, [
                'donation_type' => 'organization_subscription',
                'stage' => 'subscription_confirmation',
                'payment_intent_id' => $request->paymentIntentId,
                'customer_id' => $request->customerId,
            ]);

            Log::error('Subscription confirmation failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Failed to confirm subscription',
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a completed transaction for an organization donation
     */
    public function storeTransactionForOrgDonation(Request $request)
    {
        try {
            $request->merge([
                'anonymousToPublic' => $request->has('anonymousToPublic'),
                'completeAnonymity' => $request->has('completeAnonymity'),
            ]);

            $data = $request->validate([
                'amount' => 'required|numeric|min:1',
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email',
                'message' => 'nullable|string|max:255',
                'address' => 'required|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'anonymousToPublic' => 'nullable|boolean',
                'completeAnonymity' => 'nullable|boolean',
                'paymentIntentId' => 'required|string',
                'paymentFrequency' => 'required|in:oneTime,daily,monthly,quarterly,annually',
                'customerId' => 'nullable|string',
                'subscriptionId' => 'nullable|string',
            ]);

            DB::beginTransaction();

            if ($data['completeAnonymity']) {
                $data['anonymousToPublic'] = true;
            }

            // Retrieve subscription details from Stripe if recurring
            if ($data['subscriptionId']) {
                $subscription = Subscription::retrieve($data['subscriptionId']);

                // For subscriptions without trial_end, use current_period_start or current time
                $startDate = $subscription->current_period_start
                    ? Carbon::createFromTimestamp($subscription->current_period_start)
                    : now();

                $nextPaymentDate = match ($data['paymentFrequency']) {
                    'daily' => $startDate->copy()->addDay(),
                    'monthly' => $startDate->copy()->addMonth(),
                    'quarterly' => $startDate->copy()->addMonths(3),
                    'annually' => $startDate->copy()->addYear(),
                    default => null,
                };

                $updatedRows = DB::table('subscriptions')
                    ->where('subscription_id', $data['subscriptionId'])
                    ->update([
                        'status' => 'active',
                        'start_date' => $startDate,
                        'next_payment_at' => $nextPaymentDate,
                        'updated_at' => now(),
                    ]);

                // Log if subscription was not found in database
                if ($updatedRows === 0) {
                    Log::warning('Subscription not found in database during transaction storage', [
                        'subscription_id' => $data['subscriptionId'],
                        'customer_id' => $data['customerId'],
                    ]);
                }
            }

            $donor = Donor::create([
                'name' => trim($data['firstName'] . ' ' . $data['lastName']),
                'email' => $data['email'],
                'address' => $data['address'],
                'city' => $data['city'],
                'state' => $data['state'],
                'message_for_fundraiser' => $data['message'],
                'amount_donate' => $data['amount'],
                'payment_frequency' => $data['paymentFrequency'],
                'anonymous_for_public' => $data['anonymousToPublic'] ?? false,
                'anonymous_for_all' => $data['completeAnonymity'] ?? false,
                'customer_id' => $data['customerId'],
                'subscription_id' => $data['subscriptionId'],
            ]);

            $paymentIntent = PaymentIntent::retrieve($data['paymentIntentId']);
            $paymentMethod = $paymentIntent->payment_method_types[0];

            Transaction::create([
                'payment_intent_id' => $data['paymentIntentId'],
                'amount' => $data['amount'],
                'currency' => 'usd',
                'status' => 'success',
                'payment_method' => $paymentMethod,
                'description' => "Donated to Organization",
                'is_refunded' => false,
                'refund_amount' => 0,
                'refund_id' => null,
                'refunded_at' => null,
            ]);

            DB::commit();
            Event::dispatch(new PaymentRecieved((float) $data['amount'], $donor));

            return response()->json([
                'success' => true,
                'message' => 'Transaction stored successfully.',
                'redirect_url' => route('successPage'),
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation error',
                'messages' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();

            // Record failed database storage
            $this->failedTransactionService->recordDatabaseStorageFailure(
                $request->paymentIntentId ?? 'unknown',
                $request,
                $e,
                [
                    'donation_type' => 'organization_donation',
                    'stage' => 'transaction_storage',
                    'customer_id' => $request->customerId ?? null,
                    'subscription_id' => $request->subscriptionId ?? null,
                ]
            );

            Log::error('Organization transaction storage failed: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Transaction error',
                'message' => 'Unable to store transaction. Please contact support.'
            ], 500);
        }
    }

    /**
     * Handle client-side payment failures
     * This method is called when payment confirmation fails on the frontend
     */
    public function handleClientSidePaymentFailure(Request $request)
    {
        try {
            $request->validate([
                'paymentIntentId' => 'required|string',
                'errorCode' => 'required|string',
                'errorMessage' => 'required|string',
                'errorType' => 'nullable|string',
                'amount' => 'nullable|numeric',
                'customerEmail' => 'nullable|email',
                'customerName' => 'nullable|string',
                'donationType' => 'nullable|string',
                'donatedToSlug' => 'nullable|string',
                'donationMessage' => 'nullable|string',
                'paymentMethodType' => 'nullable|string',
                'failureStage' => 'nullable|string',
            ]);

            // Record the failed payment directly using the service
            FailedTransaction::create([
                'payment_intent_id' => $request->paymentIntentId,
                'amount' => $request->amount,
                'currency' => 'usd',
                'failure_stage' => $request->failureStage ?? 'client_side_payment_confirmation',
                'failure_code' => $request->errorCode,
                'failure_message' => $request->errorMessage,
                'failure_reason' => $this->categorizeClientSideFailure($request->errorCode),
                'failure_description' => $request->errorType ?? 'client_side_payment_failure',
                'customer_email' => $request->customerEmail,
                'customer_name' => $request->customerName,
                'donated_to_slug' => $request->donatedToSlug,
                'donation_type' => $request->donationType ?? 'unknown',
                'donation_message' => $request->donationMessage,
                'payment_method_type' => $request->paymentMethodType,
                'error_details' => [
                    'error_code' => $request->errorCode,
                    'error_message' => $request->errorMessage,
                    'error_type' => $request->errorType ?? 'client_side_payment_failure',
                    'failure_stage' => $request->failureStage ?? 'client_side_payment_confirmation',
                ],
                'request_data' => $request->all(),
                'status' => 'failed',
                'failed_at' => now(),
                'metadata' => [
                    'donation_type' => $request->donationType ?? 'unknown',
                    'donated_to_slug' => $request->donatedToSlug ?? null,
                    'error_code' => $request->errorCode,
                    'error_type' => $request->errorType ?? 'client_side_payment_failure',
                    'payment_method_type' => $request->paymentMethodType ?? null,
                    'customer_email' => $request->customerEmail ?? null,
                    'customer_name' => $request->customerName ?? null,
                ],
            ]);

            Log::info('Client-side payment failure recorded', [
                'payment_intent_id' => $request->paymentIntentId,
                'error_code' => $request->errorCode,
                'error_message' => $request->errorMessage,
                'donation_type' => $request->donationType,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment failure recorded successfully.',
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'error' => 'Validation error',
                'messages' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to record client-side payment failure: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'error' => 'Failed to record payment failure',
                'message' => 'Unable to record payment failure. Please contact support.',
            ], 500);
        }
    }

    /**
     * Categorize client-side failure codes
     */
    private function categorizeClientSideFailure(string $errorCode): string
    {
        $categories = [
            'card_declined' => 'card_declined',
            'insufficient_funds' => 'insufficient_funds',
            'expired_card' => 'expired_card',
            'incorrect_cvc' => 'incorrect_cvc',
            'processing_error' => 'processing_error',
            'invalid_request' => 'invalid_request',
            'authentication_required' => 'authentication_required',
            'rate_limit_exceeded' => 'rate_limit',
            'invalid_payment_method' => 'invalid_payment_method',
            'card_not_supported' => 'card_not_supported',
            'currency_not_supported' => 'currency_not_supported',
            'duplicate_transaction' => 'duplicate_transaction',
            'fraudulent' => 'fraudulent',
            'network_error' => 'network_error',
            'timeout' => 'timeout',
            'server_error' => 'server_error',
            'payment_intent_payment_attempt_failed' => 'payment_processing',
            'payment_intent_unexpected_state' => 'payment_processing',
            'payment_intent_invalid_parameter' => 'invalid_request',
            'payment_intent_authentication_failure' => 'authentication_required',
            'payment_intent_incompatible_payment_method' => 'invalid_payment_method',
            'payment_intent_mandate_invalid' => 'invalid_request',
            'payment_intent_payment_method_failed' => 'payment_processing',
            'payment_intent_processing_error' => 'processing_error',
            'payment_intent_requires_action' => 'authentication_required',
            'payment_intent_requires_payment_method' => 'invalid_payment_method',
        ];

        return $categories[$errorCode] ?? 'unknown';
    }

    /**
     * Display the success page after a successful payment
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function successPage(Request $request)
    {
        return view('donor.success');
    }

    /**
     * Get user-friendly error message from Stripe exception
     */
    private function getUserFriendlyErrorMessage(Exception $e): string
    {
        if ($e instanceof CardException) {
            $declineCode = $e->getDeclineCode();

            $messages = [
                'insufficient_funds' => 'Your card has insufficient funds.',
                'card_declined' => 'Your card was declined.',
                'expired_card' => 'Your card has expired.',
                'incorrect_cvc' => 'The security code (CVC) is incorrect.',
                'processing_error' => 'An error occurred while processing your card.',
                'card_not_supported' => 'This card type is not supported.',
                'fraudulent' => 'This transaction was flagged as potentially fraudulent.',
            ];

            return $messages[$declineCode] ?? 'Your card was declined. Please try a different card.';
        }

        if ($e instanceof RateLimitException) {
            return 'Too many requests. Please wait a moment and try again.';
        }

        if ($e instanceof ApiConnectionException) {
            return 'Network error. Please check your connection and try again.';
        }

        if ($e instanceof AuthenticationException) {
            return 'Payment service authentication error. Please contact support.';
        }

        if ($e instanceof InvalidRequestException) {
            return 'Invalid payment request. Please check your information and try again.';
        }

        return 'Payment processing error. Please try again later.';
    }
}
