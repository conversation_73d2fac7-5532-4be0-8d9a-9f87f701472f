<?php

namespace App\Http\Controllers;

use App\Models\Donor;
use App\Models\Message;
use App\Services\AdminService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Role;
use App\Models\Subscription;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;

class AdminController extends Controller
{

    protected $adminService;

    public function __construct(AdminService $adminService)
    {
        $this->adminService = $adminService;
    }


    public function dashboard(Request $request): View
    {
        try {

            $data = $this->adminService->getDashboardData($request);


            return view('Admin.dashboard', compact('data'));
        } catch (Exception $e) {

            dd($e->getMessage());
            return view('Errors.errors500', compact('e'));
        }
    }


    public function getAllUsers(Request $request)
    {
        $query = User::query()
            ->select(
                'users.id',
                'users.name',
                'users.city',
                'users.slug',
                'users.fundraising_goal',
                'users.profile_photo',
                'users.email',
                DB::raw('COALESCE(SUM(user_donors.amount), 0) as total_collected')
            )
            ->leftJoin('user_donors', 'users.id', '=', 'user_donors.user_id')
            ->join('role_user', function ($join) {
                $join->on('users.id', '=', 'role_user.user_id')
                    ->where('role_user.role_id', function ($query) {
                        $query->select('id')
                            ->from('roles')
                            ->where('name', 'user')
                            ->limit(1);
                    });
            })
            ->groupBy('users.id', 'users.name', 'users.email', 'users.city', 'users.slug', 'users.fundraising_goal', 'users.profile_photo');


        if ($request->filled('search')) {
            $search = trim($request->search);
            $query->where(function ($q) use ($search) {
                $q->where('users.name', 'like', "%$search%")
                    ->orWhere('users.email', 'like', "%$search%")
                    ->orWhere('users.city', 'like', "%$search%")
                    ->orHaving('total_collected', '=', (int)$search);
            });
        }


        $sortBy = $request->input('sortBy', null);
        $sortOrder = 'desc';

        if ($sortBy === 'city') {
            $query->orderBy('users.city', 'asc');
        } elseif ($sortBy === 'name') {
            $query->orderByRaw("LOWER(users.name) ASC");
        } elseif ($sortBy === 'total_collected') {
            $query->orderByRaw('COALESCE(SUM(user_donors.amount), 0) DESC');
        }


        if ($sortBy !== 'total_collected') {
            $query->orderBy('users.id', 'desc');
        }

        $fundraisers = $query->paginate(12);


        $fundraisers->setCollection(
            $fundraisers->getCollection()->transform(function ($user) {
                $user->donationPageUrl = route('donationPage', ['slug' => $user->slug]);
                $user->profile_photo = !empty($user->profile_photo)
                    ? Storage::url($user->profile_photo)
                    : Storage::url('uploads/default.png');
                return $user;
            })
        );

        return response()->json($fundraisers);
    }





    public function getAllDonors(Request $request)
    {
        $query = Donor::with(['users' => function ($query) {
            $query->select('users.id', 'users.name');
        }])
            ->when($request->search, function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('email', 'like', '%' . $request->search . '%');
            });

        $sortBy = $request->input('sortBy', null);
        $sortOrder = $request->input('sortOrder', 'desc');

        if ($sortBy === 'name') {
            $query->orderByRaw("LOWER(name) asc");
        } elseif ($sortBy === 'amount_donate') {
            $query->orderBy('amount_donate', $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $donors = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $donors
        ]);
    }


    public function donationList()
    {

        $totalAmountCollected = Donor::sum('amount_donate');
        $totalDonors = Donor::count();
        $averageDonation = Donor::avg('amount_donate');
        $medianDonation = Donor::orderBy('amount_donate')->skip((int)($totalDonors / 2))->value('amount_donate');
        $biggestDonation = Donor::orderBy('amount_donate', 'desc')->first();
        $repeatDonors = Donor::select('email')
            ->groupBy('email')
            ->havingRaw('COUNT(*) > 1')
            ->get()
            ->count();

        $anonymousDonors = Donor::where('anonymous_for_public', true)->where('anonymous_for_all', true)->count();


        $donationsThisWeek = Donor::where('created_at', '>=', now()->startOfWeek())->sum('amount_donate');
        $donationsThisMonth = Donor::where('created_at', '>=', now()->startOfMonth())->sum('amount_donate');
        $donationsThisYear = Donor::where('created_at', '>=', now()->startOfYear())->sum('amount_donate');


        $topDonors = Donor::select('name', 'email', DB::raw('SUM(amount_donate) as total'))
            ->groupBy('name', 'email')
            ->orderByDesc('total')
            ->limit(10)
            ->get();


        $recentDonors = Donor::latest()->limit(10)->get();


        $donorsByCity = Donor::select('city', DB::raw('COUNT(*) as total'))
            ->whereNotNull('city')
            ->where('city', '!=', '')
            ->groupBy('city')
            ->orderByDesc('total')
            ->get();




        $donationTrend = Donor::select(
            DB::raw('DATE(created_at) as date'),
            DB::raw('SUM(amount_donate) as total')
        )
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('Admin.donors', [
            'totalAmountCollected' => $totalAmountCollected,
            'totalDonors' => $totalDonors,
            'averageDonation' => $averageDonation,
            'medianDonation' => $medianDonation,
            'biggestDonation' => $biggestDonation,
            'repeatDonors' => $repeatDonors,
            'anonymousDonors' => $anonymousDonors,
            'donationsThisWeek' => $donationsThisWeek,
            'donationsThisMonth' => $donationsThisMonth,
            'donationsThisYear' => $donationsThisYear,
            'topDonors' => $topDonors,
            'recentDonors' => $recentDonors,
            'donorsByCity' => $donorsByCity,
            'donationTrend' => $donationTrend,
        ]);
    }


    public function exportDonors(): StreamedResponse
    {
        $fileName = 'donors_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$fileName\"",
        ];

        $callback = function () {
            $handle = fopen('php://output', 'w');
            fputcsv($handle, [
                'Name',
                'Email',
                'Address',
                'Message',
                'Amount Donated',
                'Created At'
            ]);


            $donors = Donor::all();

            foreach ($donors as $donor) {
                fputcsv($handle, [
                    $donor->name,
                    $donor->email,
                    $donor->address,
                    $donor->message_for_fundraiser,
                    $donor->amount_donate,
                    $donor->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($handle);
        };

        return response()->stream($callback, 200, $headers);
    }



    public function editCurrentYearGoal(Request $request): JsonResponse
    {
        try {
            $currentYear = now()->year;

            $validatedData = $request->validate([
                'goalAmount' => 'required|numeric|min:1',
                'year'   => "required|integer|in:$currentYear",
            ]);

            DB::table('settings')->updateOrInsert(
                ['year' => $validatedData['year']],
                ['goal_amount' => $validatedData['goalAmount']]
            );

            return response()->json([
                'success' => true,
                'message' => 'Goal updated successfully!',
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage(),
            ], 500);
        }
    }


    public function mailView(): View
    {

        return view('Admin.send-mail');
    }

    public function faqs(): View
    {
        $faqs = DB::table('faqs')->paginate(30);


        return view('Admin.faqs', compact('faqs'));
    }


    public function faqStore(Request $request): JsonResponse
    {
        $faq = $this->adminService->storeFaq($request);

        return response()->json([
            'success' => true,
            'message' => 'FAQ created successfully!',
            'data' => $faq,
        ], 201);
    }


    public function faqUpdate(Request $request): JsonResponse
    {


        $faq = $this->adminService->updateFaq($request);

        return response()->json([
            'success' => true,
            'message' => 'FAQ updated successfully!',
            'data' => $faq,
        ], 200);
    }


    public function faqDelete(Request $request): JsonResponse
    {
        $this->adminService->deleteFaq($request);

        return response()->json([
            'success' => true,
            'message' => 'FAQ deleted successfully!',
        ]);
    }


    public function addAdminAccount(Request $request): JsonResponse
    {

        $admin = $this->adminService->storeAdmin($request);

        return response()->json([
            'success' => true,
            'message' => 'Admin Account created successfully!',
            'data' => $admin,
        ], 201);
    }


    public function addUserAccount(Request $request): JsonResponse
    {

        $admin = $this->adminService->storeUser($request);

        return response()->json([
            'success' => true,
            'message' => 'User Account created successfully!',
            'data' => $admin,
        ], 201);
    }

    public function editUser($id)
    {
        $user = User::findOrFail($id);

        return view('Admin.partials.edit-user-form', compact('user'));
    }

    public function storeEditedUser(Request $request)
    {

        $user = $this->adminService->storeEditedUser($request);


        return response()->json([
            'success' => true,
            'message' => 'User Account updated successfully!',
            'data' => $user,

        ], 201);
    }



    public function deleteUser(Request $request)
    {
        $id = $request->input('id');

        $user = User::find($id);

        if ($user) {
            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'User deleted successfully.'
            ], 200);
        }

        return response()->json([
            'success' => false,
            'message' => 'User not found.'
        ], 404);
    }


    //for testing remove it later
    public function subscriptions()
    {

        $subscriptions = Subscription::all();
        $donors = Donor::all();

        return view('Admin.subscriptions', compact('subscriptions', 'donors'));
    }
}
