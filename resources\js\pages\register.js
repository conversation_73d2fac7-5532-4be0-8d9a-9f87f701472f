 import { createSlider } from '../appTwo.js';

document.addEventListener("DOMContentLoaded", function () {
    // Dont Remove the comment
    // initializing the rich text editor, dont move it down other wise the message will not append properly
    tinymce.init({
        selector: "textarea#message",
        height: 300,
        plugins: [
            "lists",
            "advlist",
            "autolink",
            "link",
            "image",
            "charmap",
            "preview",
            "anchor",
            "searchreplace",
            "wordcount",
            "code",
            "fullscreen",
            "insertdatetime",
            "table",
        ],
        toolbar:
            "undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen",
        menubar: false,
        content_style: `
             body {
                 font-family: Helvetica, Arial, sans-serif;
                 font-size: 16px;
             }
         `,

        setup: function (editor) {
            editor.on("init", function () {
                editor.getContainer().style.width = "100%";

                const editorContainer = editor.getContainer();
                editorContainer.style.border = "1px solid #19345e";
                editorContainer.style.borderRadius = "4px";

                editor.on("focus", function () {
                    editorContainer.style.border = "1px solid #154da3";
                    editorContainer.style.boxShadow =
                        "0 0 0 0.2rem rgba(21, 77, 163, 0.25)";
                });

                editor.on("blur", function () {
                    editorContainer.style.border = "1px solid #19345e";
                    editorContainer.style.boxShadow = "none";
                });
            });


            let resizeTimer;
            let isFocusingOtherField = false;

            // Track focus on other input fields
            document.querySelectorAll('input, select').forEach(input => {
                input.addEventListener('focus', function() {
                    isFocusingOtherField = true;
                });

                input.addEventListener('blur', function() {
                    setTimeout(() => {
                        isFocusingOtherField = false;
                    }, 100);
                });
            });

            window.addEventListener("resize", function () {

                if (!isFocusingOtherField) {
                    clearTimeout(resizeTimer);
                    resizeTimer = setTimeout(function() {
                        editor.execCommand("mceAutoResize");
                    }, 300);
                }
            });
        },

        autoresize_on_init: true,
        resize: true,
    });

    const dropZone = document.getElementById("dropZone");
    const fileUpload = document.getElementById("fileUpload");
    const imagePreview = document.getElementById("imagePreview");
    const imagePreviewContainer = document.getElementById(
        "imagePreviewContainer"
    );
    const removeImageBtn = document.getElementById("removeImage");
    const manualUploadBtn = document.getElementById("manualUploadBtn");
    const dropZoneTrigger = document.getElementById("dropZoneTrigger");

    if (dropZoneTrigger) {
        dropZoneTrigger.addEventListener("click", function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log("Trigger clicked");
            if (fileUpload) {
                fileUpload.click();
            }
        });
    }

    if (dropZone) {
        dropZone.addEventListener("click", function (e) {
            if (
                e.target === dropZone ||
                e.target === dropZoneTrigger ||
                e.target.classList.contains("drop-zone-text")
            ) {
                console.log("Drop zone clicked");
                if (fileUpload) {
                    fileUpload.click();
                }
            }
        });
    }

    let fileToUpload;

    if (fileUpload) {
        fileUpload.addEventListener("change", handleFileUpload);
    }

    if (dropZone) {
        dropZone.addEventListener("dragover", handleDragOver);
        dropZone.addEventListener("dragleave", handleDragLeave);
        dropZone.addEventListener("drop", handleDrop);
    }

    if (manualUploadBtn) {
        manualUploadBtn.addEventListener("click", () => {
            if (fileUpload) {
                fileUpload.click();
            }
        });
    }

    if (removeImageBtn) {
        removeImageBtn.addEventListener("click", (e) => {
            e.stopPropagation();
            if (fileUpload) {
                fileUpload.value = "";
            }
            if (imagePreview) {
                imagePreview.style.display = "none";
            }
            if (imagePreviewContainer) {
                imagePreviewContainer.style.display = "none";
            }
            if (dropZoneTrigger) {
                dropZoneTrigger.style.display = "flex";
            }
        });
    }

    function handleFileUpload(event) {
        const file = event.target.files[0];
        if (file) {
            fileToUpload = file;
            console.log("File name:", file.name);
            displayImagePreview(file);
        }
    }

    function handleDragOver(event) {
        event.preventDefault();
        if (dropZone) {
            dropZone.classList.add("drag-over");
        }
    }

    function handleDragLeave(event) {
        event.preventDefault();
        if (dropZone) {
            dropZone.classList.remove("drag-over");
        }
    }

    function handleDrop(event) {
        event.preventDefault();
        if (dropZone) {
            dropZone.classList.remove("drag-over");
        }
        const file = event.dataTransfer.files[0];
        if (file) {
            fileToUpload = file;
            console.log("File name:", file.name);
            displayImagePreview(file);
        }
    }

    function displayImagePreview(file) {
        const reader = new FileReader();
        reader.onload = function (e) {
            if (imagePreview) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = "block";
            }
            if (imagePreviewContainer) {
                imagePreviewContainer.style.display = "flex";
            }
            if (dropZoneTrigger) {
                dropZoneTrigger.style.display = "none";
            }
            if (removeImageBtn) {
                removeImageBtn.style.display = "block";
            }
        };
        reader.readAsDataURL(file);
    }

    // handling the form submission
    const form = document.querySelector("form");

    form.addEventListener("submit", function (event) {
        event.preventDefault();

        const formData = new FormData(form);
        let isValid = true;
        let firstInvalidField = null;

        const requiredFields = [
            "name",
            "email",
            "address",
            "file",
            "city",
            "state",
            "password",
            "fund_raise_message",
            "fund_raise_goal",
            "commit_fundraising",
            "use_photo_videos",
            "liability_waiver",
        ];

        // Add file to formData if it exists
        if (fileToUpload) {
            formData.append("file", fileToUpload);
        }

        requiredFields.forEach((field) => {
            const input = form.querySelector(`[name="${field}"]`);

            // Special handling for TinyMCE editor
            if (field === "fund_raise_message" && input) {
                const editor = tinymce.get(input.id);
                const content = editor.getContent({ format: "text" }).trim();
                const editorContainer = editor.getContainer();

                if (!content) {
                    isValid = false;
                    editorContainer.classList.add("is-invalid");
                    if (!firstInvalidField) firstInvalidField = editorContainer;
                } else {
                    editorContainer.classList.remove("is-invalid");
                }
                return; // Skip further validation for this field
            }

            // Handle regular fields
            if (input) {
                const isCheckbox = input.type === "checkbox";
                const isFileInput = input.type === "file";

                let isFieldValid = true;

                if (field === "file") {
                    isFieldValid = !!fileToUpload;

                    if (!isFieldValid) {
                        createSlider(
                            "Confirm Action",
                            "Please Upload a Profile Photo",
                            { title: "Confirm Action" }
                        );
                    }
                } else {
                    const fieldValue = formData.get(field);
                    isFieldValid = isCheckbox
                        ? input.checked
                        : fieldValue && fieldValue.trim() !== "";
                }

                if (!isFieldValid) {
                    isValid = false;
                    input.classList.add("is-invalid");
                    input.style.borderColor = "";
                    if (!firstInvalidField) {
                        firstInvalidField = input;
                    }
                } else {
                    input.classList.remove("is-invalid");
                    input.style.borderColor = "#2575fc";
                }
            }
        });

        // Scroll to the first invalid field if any
        if (firstInvalidField) {
            firstInvalidField.scrollIntoView({
                behavior: "smooth",
                block: "center",
            });
        }

        if (isValid) {
            let registerButton = document.getElementById("register");

            registerButton.disabled = true;
            registerButton.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>`;
            registerButton.style.backgroundColor = "#19345e";

            const url = route("register.store");
            fetch(url, {
                method: "POST",
                body: formData,
                headers: {
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    if (data.success) {
                        form.reset();
                        registerButton.disabled = false;
                        registerButton.innerHTML = `
                        <span class="d-inline-flex align-items-center">
                            <span class="fas fa-check-circle text-success me-1" role="status" aria-hidden="true" style="font-size: 1em; line-height: 1;"></span>
                            Registered
                        </span>
                        `;

                        createSlider("success", data.message);
                        setTimeout(() => {
                            window.location.href = data.redirectUrl;
                        }, 2000);
                    } else {
                        registerButton.disabled = false;
                        registerButton.innerHTML = "Register Now";
                        registerButton.style.backgroundColor = "#f05522";

                        if (data.errors) {
                            handleValidationErrors(data.errors);
                        }

                        createSlider("error", data.message, { title: "Error" });
                    }
                })
                .catch((error) => {
                    registerButton.style.backgroundColor = "#f05522";
                    registerButton.disabled = false;
                    registerButton.innerHTML = "Register Now";
                    console.error("Error:", error);
                    createSlider(
                        "error",
                        "An error occurred. Please try again later.",
                        { title: "Error" }
                    );
                });
        }
    });

    //handling the password toggling

    const passwordInput = document.getElementById("password");
    const toggleButton = document.querySelector(".password-toggle");

    let isPasswordVisible = false;

    toggleButton.addEventListener("click", function () {
        isPasswordVisible = !isPasswordVisible;
        passwordInput.type = isPasswordVisible ? "text" : "password";

        toggleButton.innerHTML = isPasswordVisible
            ? `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye" viewBox="0 0 16 16">
                                                   <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8M1.173 8a13 13 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5s3.879 1.168 5.168 2.457A13 13 0 0 1 14.828 8q-.086.13-.195.288c-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5s-3.879-1.168-5.168-2.457A13 13 0 0 1 1.172 8z"/>
                                                   <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5M4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"/>
                                                 </svg> `
            : `
   <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye-slash" viewBox="0 0 16 16">
                                                   <path d="M13.359 11.238C15.06 9.72 16 8 16 8s-3-5.5-8-5.5a7 7 0 0 0-2.79.588l.77.771A6 6 0 0 1 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13 13 0 0 1 14.828 8q-.086.13-.195.288c-.335.48-.83 1.12-1.465 1.755q-.247.248-.517.486z"/>
                                                   <path d="M11.297 9.176a3.5 3.5 0 0 0-4.474-4.474l.823.823a2.5 2.5 0 0 1 2.829 2.829zm-2.943 1.299.822.822a3.5 3.5 0 0 1-4.474-4.474l.823.823a2.5 2.5 0 0 0 2.829 2.829"/>
                                                   <path d="M3.35 5.47q-.27.24-.518.487A13 13 0 0 0 1.172 8l.195.288c.335.48.83 1.12 1.465 1.755C4.121 11.332 5.881 12.5 8 12.5c.716 0 1.39-.133 2.02-.36l.77.772A7 7 0 0 1 8 13.5C3 13.5 0 8 0 8s.939-1.721 2.641-3.238l.708.709zm10.296 8.884-12-12 .708-.708 12 12z"/>
                                                 </svg>
   `;
    });

    function handleValidationErrors(errors) {
        document
            .querySelectorAll(".is-invalid")
            .forEach((el) => el.classList.remove("is-invalid"));
        document
            .querySelectorAll(".invalid-feedback")
            .forEach((el) => el.remove());

        let firstErrorField = null;

        Object.entries(errors).forEach(([field, messages]) => {
            const input = document.querySelector(`[name="${field}"]`);

            if (field === "fund_raise_message" && tinymce.get(input?.id)) {
                const editor = tinymce.get(input.id);
                const container = editor.getContainer();

                container.classList.add("is-invalid");

                if (!firstErrorField) {
                    firstErrorField = container;
                }

                const errorDiv = document.createElement("div");
                errorDiv.className = "invalid-feedback mt-1";
                errorDiv.textContent = messages[0];
                container.parentElement.appendChild(errorDiv);
            } else if (input) {
                input.classList.add("is-invalid");
                if (!firstErrorField) {
                    firstErrorField = input;
                }

                const errorDiv = document.createElement("div");
                errorDiv.className = "invalid-feedback";
                errorDiv.textContent = messages[0];
                input.parentElement.appendChild(errorDiv);
            }
        });

        if (firstErrorField) {
            firstErrorField.scrollIntoView({
                behavior: "smooth",
                block: "center",
            });
        }
    }
});
