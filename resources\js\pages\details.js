import { createSlider } from '../appTwo.js';
document.addEventListener('DOMContentLoaded', function() {
const stripeKey = document.querySelector('meta[name="stripe-key"]')?.content;
const stripe = Stripe(stripeKey);
var elements = stripe.elements();

// Check if mobile
const isMobile = window.innerWidth < 768;

// Create card with responsive options
var cardElement = elements.create('card', {
    style: {
        base: {
            color: "#32325d",
            fontSize: '16px',
            fontWeight: '500',
            fontFamily: 'Roboto, Open Sans, Segoe UI, sans-serif',

            fontSmoothing: 'antialiased',
            ':-webkit-autofill': {
              color: '#fce883',
            },
            "::placeholder": {
                color: "#aab7c4"
            },
            iconColor: "#19345E"
        },
        invalid: {
            color: "#e74c3c",
            iconColor: "#e74c3c"
        }
    },

    hidePostalCode: false
});

// Add mobile class to container if on mobile
if (isMobile) {
    document.querySelector('.stripe-card-element').classList.add('mobile-card-element');
}

cardElement.mount('#card-element');

cardElement.addEventListener('change', function(event) {
    var displayError = document.getElementById('card-errors');
    if (event.error) {
        displayError.textContent = event.error.message;
        displayError.classList.add('show');
    } else {
        displayError.textContent = '';
        displayError.classList.remove('show');
    }
});

document.getElementById('donationForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    // Show Full-Screen Loader
    showLoader("Processing Payment...");

    const form = document.getElementById('donationForm');
    const formData = new FormData(form);
    const formObject = {};
    formData.forEach((value, key) => {
        formObject[key] = value;
    });

    try {
        // 1. Create Payment Intent
        const response = await fetch(form.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                    .getAttribute('content')
            },
            body: JSON.stringify(formObject)
        });
        const {
            clientSecret
        } = await response.json();

        if (!clientSecret) throw new Error("Failed to create Payment Intent.");

        // 2. Confirm Card Payment
        const {
            paymentIntent,
            error
        } = await stripe.confirmCardPayment(clientSecret, {
            payment_method: {
                card: cardElement,
                billing_details: {
                    name: formObject.firstName,
                    email: formObject.email,
                },
            },
        });

        if (error) {
            hideLoader();
            console.error('Payment Error:', error.message);

            // Record the payment failure
            await recordPaymentFailure({
                paymentIntentId: clientSecret.split('_secret_')[0] || 'unknown',
                errorCode: error.code || 'unknown',
                errorMessage: error.message,
                errorType: error.type || 'card_error',
                amount: formObject.amount,
                customerEmail: formObject.email,
                customerName: formObject.firstName + ' ' + (formObject.lastName || ''),
                donationType: 'user_donation',
                donatedToSlug: formObject.donatedTo,
                donationMessage: formObject.message,
                paymentMethodType: 'card',
                failureStage: 'client_side_payment_confirmation'
            });

            createSlider('error', error.message);
        } else if (paymentIntent.status === 'succeeded') {
            console.log('Payment successful!');

            // 3. Send final confirmation to backend
            formObject.paymentIntentId = paymentIntent.id;

            fetch('/store-transaction', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector(
                            'meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(formObject)
                })
                .then(res => res.json())
                .then(data => {
                    if (data.success) {

                        showSuccessAnimation(() => {
                            window.location.href = data.redirect_url;
                        });
                    } else {
                        hideLoader();
                        createSlider('error', data.message);
                    }
                })
                .catch(err => {
                    hideLoader();
                    createSlider('error', err);
                });
        }
    } catch (err) {
        hideLoader();
        console.error('Unexpected Error:', err);
    }
});

// Show Full-Screen Loader
function showLoader(message) {
    if (!document.getElementById('full-screen-loader')) {
        const loader = document.createElement('div');
        loader.id = 'full-screen-loader';
        loader.innerHTML = `
        <div class="loader-icon">
            <div class="spinner-border" role="status"></div>
        </div>
        <p>${message}</p>
    `;
        document.body.appendChild(loader);
    }
}

// Hide Loader
function hideLoader() {
    const loader = document.getElementById('full-screen-loader');
    if (loader) loader.remove();
}


function showSuccessAnimation(callback) {
    const loader = document.getElementById('full-screen-loader');
    if (loader) {
        loader.innerHTML = `
        <div class="success-animation">
            🎉
        </div>
        <p>Thank You for Your Donation!</p>
    `;
        setTimeout(() => {
            callback();
        }, 2000);
    }
}

// Function to record payment failures
async function recordPaymentFailure(failureData) {
    try {
        const response = await fetch('/record-payment-failure', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(failureData)
        });

        const result = await response.json();

        if (result.success) {
            console.log('Payment failure recorded successfully');
        } else {
            console.error('Failed to record payment failure:', result.message);
        }
    } catch (error) {
        console.error('Error recording payment failure:', error);
    }
}

});
