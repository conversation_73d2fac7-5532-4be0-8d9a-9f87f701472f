@extends('layouts.app')

@section('title', 'Donors List')

@section('no-header-footer', 'true')

@section('content')

    @include('layouts.admin-nav')

    <section>
        <div class="container-fluid px-4 py-5">
            <div class="dashboard-header animated">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="dashboard-title">Donor Analytics Dashboard</h1>
                        <p class="text-white opacity-75">Track, analyze, and optimize your donation campaigns</p>
                    </div>
                    <div>
                        <button class="export-btn">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                    </div>
                </div>
            </div>
            <div class="row g-4 mb-5">
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card animated delay-1">
                        <div class="d-flex justify-content-between">

                                <div>
                                    <h6 class="card-title">Total Donations</h6>
                                    <h2 class="stat-value" id="totalDonations">${{ number_format($totalAmountCollected, 2) }}</h2>
                                    <div>
                                        <span class="trend-indicator">
                                            <i class="fas fa-arrow-up me-1"></i>
                                            {{ ($totalAmountCollected > 0 && isset($donationsThisWeek) && $donationsThisWeek >= 0)
                                                ? number_format(($donationsThisWeek / $totalAmountCollected) * 100, 1)
                                                : '0' }}%
                                        </span>
                                        <span class="trend-text">this week</span>
                                    </div>
                                </div>
                            <div class="stat-icon">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                        </div>
                    </div>
                </div>
              <div class="col-xl-3 col-md-6">
                <div class="stat-card premium animated delay-2">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Donors</h6>
                            <h2 class="stat-value" id="totalDonors">{{ number_format($totalDonors) }}</h2>
                            <div>
                                <span class="trend-indicator"
                                    style="background: rgba(25, 52, 94, 0.1); color: var(--secondary);">
                                    <i class="fas fa-circle me-1"></i>
                                    {{ ($totalDonors > 0 && isset($repeatDonors) && $repeatDonors >= 0)
                                        ? number_format(($repeatDonors / $totalDonors) * 100, 1)
                                        : '0' }}%
                                </span>
                                <span class="trend-text">repeat donors</span>
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card animated delay-3">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Average Donation</h6>
                                <h2 class="stat-value" id="avgDonation">${{ number_format($averageDonation, 2) }}</h2>
                                <div>
                                    <span class="trend-indicator"
                                        style="background: rgba(255, 181, 71, 0.1); color: var(--warning);">
                                        <i class="fas fa-circle me-1"></i>Median: ${{ number_format($medianDonation, 2) }}
                                    </span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
             <div class="col-xl-3 col-md-6">
                    <div class="stat-card premium animated delay-4">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Biggest Donation</h6>
                                <h2 class="stat-value" id="biggestDonation">
                                    ${{ $biggestDonation
                                        ? number_format($biggestDonation->amount_donate, 2)
                                        : '0.00' }}
                                </h2>
                                <div>
                                    <span class="trend-text">
                                        From: {{ $biggestDonation
                                            ? $biggestDonation->name
                                            : 'No donations yet' }}
                                    </span>
                                </div>
                            </div>
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-5">
                <div class="col-lg-8">
                    <div class="chart-card animated delay-1">
                        <div class="card-header">
                            <h5 class="card-title">Donation Trends - Last 7 Days</h5>
                            <div class="d-flex align-items-center">
                                <div class="d-flex align-items-center me-3">
                                    <div style="width: 12px; height: 12px; background: var(--primary); border-radius: 3px;"
                                        class="me-2"></div>
                                    <span style="font-size: 0.85rem; color: #97a4b7;">Donations</span>
                                </div>
                                <div class="form-group mb-0">
                                    <div style="border-radius: 6px; font-size: 0.85rem;">
                                        <small>Last 7 days</small>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="donationTrendChart"></canvas>
                        </div>
                    </div>
                </div>
                            <div class="col-lg-4">
                <div class="chart-card animated delay-2">
                    <div class="card-header">
                        <h5 class="card-title">Time Period Summary</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="time-period-item">
                            <div>
                                <p class="period-label">This Week</p>
                                <h4 class="period-value">${{ number_format($donationsThisWeek, 2) }}</h4>
                            </div>
                            <div class="progress-container">
                                <div class="custom-progress">
                                    <div class="progress-bar-custom"
                                        style="width: {{ $donationsThisYear > 0
                                            ? min(($donationsThisWeek / $donationsThisYear) * 100, 100)
                                            : 0 }}%;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="time-period-item">
                            <div>
                                <p class="period-label">This Month</p>
                                <h4 class="period-value">${{ number_format($donationsThisMonth, 2) }}</h4>
                            </div>
                            <div class="progress-container">
                                <div class="custom-progress">
                                    <div class="progress-bar-custom"
                                        style="width: {{ $donationsThisYear > 0
                                            ? min(($donationsThisMonth / $donationsThisYear) * 100, 100)
                                            : 0 }}%;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="time-period-item">
                            <div>
                                <p class="period-label">This Year</p>
                                <h4 class="period-value">${{ number_format($donationsThisYear, 2) }}</h4>
                            </div>
                            <div class="progress-container">
                                <div class="custom-progress">
                                    <div class="progress-bar-custom" style="width: 100%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>

            <div class="row mb-5">
                <div class="col-lg-6">
                    <div class="chart-card animated delay-3">
                        <div class="card-header">
                            <h5 class="card-title">Donors by City</h5>
                            <div class="form-group mb-0">
                                <div style="border-radius: 6px; font-size: 0.85rem;">
                                    <small>All Regions</small>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="donorsByCityChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="chart-card animated delay-4">
                        <div class="card-header">
                            <h5 class="card-title">Donor Statistics</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container" style="height: 220px;">
                                        <canvas id="donorTypeChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex flex-column justify-content-center h-100">
                                        <div class="donor-stat-item">
                                            <div class="stat-indicator primary"></div>
                                            <div>
                                                <p class="stat-label">One-time Donors</p>
                                                <h4 class="stat-number">
                                                    {{ $totalDonors - $repeatDonors - $anonymousDonors }}</h4>
                                            </div>
                                        </div>
                                        <div class="donor-stat-item">
                                            <div class="stat-indicator secondary"></div>
                                            <div>
                                                <p class="stat-label">Repeat Donors</p>
                                                <h4 class="stat-number">{{ $repeatDonors }}</h4>
                                            </div>
                                        </div>
                                        <div class="donor-stat-item">
                                            <div class="stat-indicator tertiary"></div>
                                            <div>
                                                <p class="stat-label">Anonymous To All Donors</p>
                                                <h4 class="stat-number">{{ $anonymousDonors }}</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Latest Donors and Top Donors -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="chart-card animated delay-3">
                        <div class="card-header">
                            <h5 class="card-title">Top Donors</h5>
                            <a href="#" class="card-link">View All</a>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table data-table">
                                    <thead>
                                        <tr>
                                            <th>Donor</th>
                                            <th>Email</th>
                                            <th class="text-end">Total Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($topDonors as $donor)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="donor-avatar primary">{{ substr($donor->name, 0, 1) }}
                                                        </div>
                                                        <span class="donor-name">{{ $donor->name }}</span>
                                                    </div>
                                                </td>
                                                <td>{{ $donor->email }}</td>
                                                <td class="text-end">${{ number_format($donor->total, 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="chart-card animated delay-4">
                        <div class="card-header">
                            <h5 class="card-title">Recent Donations</h5>
                            <a href="#" class="card-link">View All</a>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table data-table">
                                    <thead>
                                        <tr>
                                            <th>Donor</th>
                                            <th>City</th>
                                            <th>Date</th>
                                            <th class="text-end">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($recentDonors as $donor)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">

                                                        <div class="donor-avatar primary">
                                                            {{ substr($donor->name, 0, 1) }}</div>
                                                        <span class="donor-name">{{ $donor->name }}</span>
                                                    </div>
                                                </td>
                                                <td>{{ $donor->city ?? 'N/A' }}</td>
                                                <td>{{ $donor->created_at->format('M d, Y') }}</td>
                                                <td class="text-end">${{ number_format($donor->amount_donate, 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section>
        <div class="row justify-content-center mb-4">
            <div class="col-md-8">
                <div class="search-filter-container p-3 rounded-pill shadow-sm"
                    style="background: linear-gradient(145deg, #f7f9fc, #ffffff); border: 1px solid #19345E;">
                    <div class="row g-2 align-items-center">
                        <div class="col-md-7">
                            <div class="input-group">
                                <span class="input-group-text border-0 bg-transparent">
                                    <i class="fas fa-search" style="color: #19345E;"></i>
                                </span>
                                <input type="text" id="search"
                                    class="form-control border-0 bg-transparent text-dark" placeholder="Search Donors..."
                                    style="box-shadow: none;">
                            </div>
                        </div>
                        <div class="col-md-5">
                            <select id="sort-filter" class="form-select border-0 bg-transparent"
                                style="box-shadow: none; color: #F05522; font-weight: 500;">
                                <option value="">Apply Filter ↓</option>
                                <option value="amount_donate">Amount Donated</option>
                                <option value="name">Name (A-Z)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div  class="row justify-content-center">
            <div class="col-md-10">
                <div class="chart-card animated delay-3">
                    <div class="card-header">
                        <h5 class="card-title">All  Donors</h5>

                    </div>
                    <div id="table-container" class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table data-table user-table">
                                <thead>
                                    <tr>
                                        <th class="py-3 px-4">Name</th>
                                        <th class="py-3 px-4">Email</th>
                                        <th class="py-3 px-4 text-center">Address</th>
                                        <th class="py-3 px-4 text-center">Message</th>
                                        <th class="py-3 px-4">Amount Donated</th>
                                        <th class="py-3 px-4">Anonymous For</th>
                                    </tr>
                                </thead>
                                <tbody id="user-container-table-body">

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-center mt-4">
            <nav aria-label="Leaderboard pagination">
                <ul id="pagination-links" class="pagination pagination-lg">
                    <!-- Pagination dynamically added -->
                </ul>
            </nav>
        </div>
    </section>

@endsection


@push('styles')



@vite('resources/css/pages/admin-donor-list.css')



@endpush

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {

            //all donors

            let debounceTimer;

            function fetchDonors(url = "/admin/get-all-donors", search = "", sortBy = "") {
                const paginationLinks = document.getElementById("pagination-links");


                let tbody = document.getElementById("user-container-table-body");


                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="6" class="text-center py-5">
                                <div class="spinner-border" role="status" style="color: #19345E; width: 3rem; height: 3rem;"></div>
                                <p class="mt-3 text-muted">Loading donors...</p>
                            </td>
                        </tr>
                    `;
                }

                if (paginationLinks) {
                    paginationLinks.innerHTML = "";
                }

                const fullUrl = new URL(url, window.location.origin);
                fullUrl.searchParams.set("search", search);
                if (sortBy) fullUrl.searchParams.set("sortBy", sortBy);

                fetch(fullUrl)
                    .then(response => response.json())
                    .then(data => {

                        tbody = document.getElementById("user-container-table-body");


                        if (!tbody) {
                            console.error("Table body element not found");
                            return;
                        }


                        tbody.innerHTML = "";

                        if (data.data.data.length === 0) {
                            tbody.innerHTML = `
                                <tr>
                                    <td colspan="6" class="text-center py-5">
                                        <i class="fas fa-hand-holding-heart fa-4x mb-3" style="color: #19345E; opacity: 0.7;"></i>
                                        <h4 style="color: #19345E;">No donors found</h4>
                                        <p class="text-muted">Try adjusting your search filters</p>
                                    </td>
                                </tr>
                            `;
                            return;
                        }

                        data.data.data.forEach(donor => {
                            const row = document.createElement("tr");

                            row.innerHTML = `
                                <td class="py-3 px-4 fw-semibold" title="${donor.name}">${donor.name}</td>
                                <td class="py-3 px-4" title="${donor.email || 'N/A'}">${donor.email || 'N/A'}</td>
                                <td class="py-3 px-4 text-center" title="${donor.address || 'N/A'}">${donor.address || 'N/A'}</td>
                                <td class="py-3 px-4 text-center" title="${donor.message_for_fundraiser || '—'}">${donor.message_for_fundraiser || '—'}</td>
                                <td class="py-3 px-4" style="color: #F05522;" title="$${(donor.amount_donate || 0).toLocaleString()}">$${(donor.amount_donate || 0).toLocaleString()}</td>
                               <td class="py-3 px-4" title="${donor.anonymous_for_all ? 'All' : donor.anonymous_for_public ? 'Public' : 'None'}">
                                    ${donor.anonymous_for_all ? 'All' : donor.anonymous_for_public ? 'Public' : 'None'}
                                    </td>

                            `;

                            tbody.appendChild(row);
                        });

                        if (data.data.total > data.data.per_page && data.data.last_page > 1) {
                            let firstPage = document.createElement("li");
                            firstPage.className = "page-item";
                            firstPage.innerHTML =
                                `<a class="page-link" href="#"><i class="fas fa-angle-double-left"></i></a>`;
                            firstPage.addEventListener("click", e => {
                                e.preventDefault();
                                fetchDonors(data.data.prev_page_url, search, sortBy);
                            });
                            paginationLinks.appendChild(firstPage);

                            data.data.links.forEach((link, index) => {
                                if (!link.url || index === 0 || index === data.data.links.length - 1)
                                    return;

                                let pageLink = document.createElement("li");
                                pageLink.className = `page-item ${link.active ? 'active' : ''}`;
                                pageLink.innerHTML =
                                    `<a class="page-link" href="#">${link.label.replace(/<.*?>/g, '')}</a>`;
                                pageLink.addEventListener("click", e => {
                                    e.preventDefault();
                                    fetchDonors(link.url, search, sortBy);
                                });
                                paginationLinks.appendChild(pageLink);
                            });

                            let lastPage = document.createElement("li");
                            lastPage.className = "page-item";
                            lastPage.innerHTML =
                                `<a class="page-link" href="#"><i class="fas fa-angle-double-right"></i></a>`;
                            lastPage.addEventListener("click", e => {
                                e.preventDefault();
                                fetchDonors(data.data.next_page_url, search, sortBy);
                            });
                            paginationLinks.appendChild(lastPage);
                        }
                    })
                    .catch(error => {
                        console.error("Error fetching donors:", error);


                        const tbody = document.getElementById("user-container-table-body");


                        if (tbody) {
                            tbody.innerHTML = `
                                <tr>
                                    <td colspan="6" class="text-center py-5">
                                        <div class="mb-3"><i class="fas fa-exclamation-triangle fa-3x text-warning"></i></div>
                                        <h4 class="text-danger">Something went wrong</h4>
                                        <p class="text-muted">Please try again later or contact support.</p>
                                        <button class="btn btn-outline-primary mt-3" onclick="fetchDonors()">
                                            <i class="fas fa-sync-alt me-2"></i> Try Again
                                        </button>
                                    </td>
                                </tr>
                            `;
                        }
                    });
            }
            document.getElementById("search").addEventListener("input", function() {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    fetchDonors("/admin/get-all-donors", this.value.trim(), document.getElementById(
                        "sort-filter").value);
                }, 300);
            });

            document.getElementById("sort-filter").addEventListener("change", function() {
                fetchDonors("/admin/get-all-donors", document.getElementById("search").value.trim(), this
                    .value, );
            });

            fetchDonors();







            //stats data

            function formatNumber(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }

            const animatedElements = document.querySelectorAll('.animated');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = 1;
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1
            });

            animatedElements.forEach(element => {
                element.style.opacity = 0;
                element.style.transform = 'translateY(20px)';
                observer.observe(element);
            });

            Chart.defaults.font.family = "'Poppins', sans-serif";
            Chart.defaults.plugins.tooltip.backgroundColor = '#19345E';
            Chart.defaults.plugins.tooltip.titleColor = '#ffffff';
            Chart.defaults.plugins.tooltip.bodyColor = '#ffffff';
            Chart.defaults.plugins.tooltip.padding = 12;
            Chart.defaults.plugins.tooltip.cornerRadius = 8;
            Chart.defaults.plugins.tooltip.displayColors = false;
            Chart.defaults.scale.grid.color = 'rgba(0, 0, 0, 0.03)';
            Chart.defaults.scale.ticks.color = '#97a4b7';

            const trendCtx = document.getElementById('donationTrendChart').getContext('2d');

            const trendGradient = trendCtx.createLinearGradient(0, 0, 0, 300);
            trendGradient.addColorStop(0, 'rgba(240, 85, 34, 0.2)');
            trendGradient.addColorStop(1, 'rgba(240, 85, 34, 0)');

            // Get donation trend data from PHP
            const trendLabels = @json(
                $donationTrend->pluck('date')->map(function ($date) {
                    return \Carbon\Carbon::parse($date)->format('M d');
                }));

            const trendData = @json($donationTrend->pluck('total'));

           const donationTrendChart = new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: trendLabels,
        datasets: [{
            label: 'Donations',
            data: trendData,
            backgroundColor: trendGradient,
            borderColor: '#F05522',
            borderWidth: 3,
            tension: 0.4,
            pointBackgroundColor: '#ffffff',
            pointBorderColor: '#F05522',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return '$ ' + formatNumber(context.parsed.y);
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    drawBorder: false
                },
                ticks: {
                    callback: function(value) {
                        return '$ ' + formatNumber(value);
                    },
                    padding: 10,
                    font: {
                        size: 11
                    }
                }
            },
            x: {
                grid: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    padding: 10,
                    font: {
                        size: 11
                    }
                }
            }
        }
    }
});

const cityCtx = document.getElementById('donorsByCityChart').getContext('2d');
const cityData = @json($donorsByCity);

// Increase the number of cities to display (you can adjust this)
const topN = 20; // Changed from 5 to 10, or set to any number you want

const topCities = cityData.slice(0, topN);
const remainingCities = cityData.slice(topN);

const cityLabels = topCities.map(item => item.city);
const cityDataValues = topCities.map(item => item.total);

if (remainingCities.length > 0) {
    const othersTotal = remainingCities.reduce((sum, item) => sum + parseInt(item.total), 0);
    cityLabels.push('Others');
    cityDataValues.push(othersTotal);
}

// Theme-matched color palette based on your site colors
const cityColors = [
    '#19345E', // Primary Deep Blue
    '#F05522', // Primary Orange
    '#2A4A75', // Lighter Blue
    '#FF6B3D', // Lighter Orange
    '#0F1F38', // Darker Blue
    '#E04A1C', // Darker Orange
    '#3D5A8C', // Medium Blue
    '#FF8155', // Light Orange
    '#152844', // Very Dark Blue
    '#CC4419', // Dark Orange
    '#4B6BA3', // Soft Blue
    '#FFB099', // Soft Orange
    '#213654', // Deep Blue Variant
    '#F26B33', // Orange Variant
    '#5577BA', // Light Blue
    '#FF9966', // Peachy Orange
    '#0A1A2E', // Darkest Blue
    '#B8370F', // Deep Orange
    '#6688CC', // Sky Blue
    '#FFCC99', // Cream Orange
    '#162B47', // Navy Blue
    '#D94D22', // Red Orange
    '#7799DD', // Light Sky Blue
    '#FFD4B3', // Light Peach
    '#1E3A5F', // Steel Blue
    '#E85533', // Bright Orange
    '#88AAEE', // Pale Blue
    '#FFE0CC', // Very Light Peach
    '#254165', // Blue Gray
    '#F16644', // Coral Orange
    '#99BBFF', // Baby Blue
    '#FFEBDD', // Cream
    '#2C486B', // Medium Navy
    '#FF7755', // Salmon Orange
    '#AACCFF', // Very Light Blue
    '#FFF2E6', // Off White
    '#334F71', // Grayish Blue
    '#FF8866', // Light Salmon
    '#BBDDFF', // Powder Blue
    '#FFF5F0'  // Almost White
];

// Initialize the chart
const donorsByCityChart = new Chart(cityCtx, {
    type: 'doughnut',
    data: {
        labels: cityLabels,
        datasets: [{
            data: cityDataValues,
            backgroundColor: cityDataValues.map((_, index) => {
                // Use grey for "Others" (last item if it exists)
                if (index === cityDataValues.length - 1 && cityLabels[index] === 'Others') {
                    return '#6c757d';
                }
                return cityColors[index % cityColors.length];
            }),
            borderWidth: 0,
            hoverOffset: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '70%',
        plugins: {
            legend: {
                position: 'right',
                labels: {
                    boxWidth: 12,
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'rectRounded',
                    font: {
                        size: 11
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.formattedValue;
                        const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                        const percentage = Math.round((context.raw / total) * 100);
                        return `${label}: ${value} (${percentage}%)`;
                    }
                }
            }
        }
    }
});

            // Donor Type Chart - Dynamic data
            const typeCtx = document.getElementById('donorTypeChart').getContext('2d');


            const oneTimeDonors = {{ $totalDonors - $repeatDonors - $anonymousDonors }};
            const repeatDonors = {{ $repeatDonors }};
            const anonymousDonors = {{ $anonymousDonors }};

            const typeData = [oneTimeDonors, repeatDonors, anonymousDonors];
            const typeColors = ['#19345E', '#F05522', '#6c757d'];

            const donorTypeChart = new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['One-time Donors', 'Repeat Donors', 'Anonymous Donors'],
                    datasets: [{
                        data: typeData,
                        backgroundColor: typeColors,
                        borderWidth: 0,
                        hoverOffset: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '75%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.formattedValue;
                                    const total = context.dataset.data.reduce((acc, val) => acc + val,
                                        0);
                                    const percentage = Math.round((context.raw / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });


            function animateCounter(elementId, startValue, endValue, duration) {
                const element = document.getElementById(elementId);
                const range = endValue - startValue;
                const frameRate = 60;
                const totalFrames = Math.round((duration / 1000) * frameRate);
                const increment = range / totalFrames;

                let current = startValue;
                let frame = 0;

                const timer = setInterval(() => {
                    frame++;
                    current += increment;


                    if (frame >= totalFrames) {
                        current = endValue;
                        clearInterval(timer);
                    }


                    if (elementId === 'totalDonations' || elementId === 'avgDonation' || elementId ===
                        'biggestDonation') {
                        element.textContent = '$' + formatNumber(current.toFixed(2));
                    } else {
                        element.textContent = formatNumber(Math.round(current));
                    }
                }, 1000 / frameRate);
            }


           const counterObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            setTimeout(() => {
                switch (entry.target.id) {
                    case 'totalDonations':
                        animateCounter('totalDonations', 0,
                            {{ $totalAmountCollected }}, 2000);
                        break;
                    case 'totalDonors':
                        animateCounter('totalDonors', 0, {{ $totalDonors }},
                            2000);
                        break;
                    case 'avgDonation':
                        animateCounter('avgDonation', 0,
                            {{ $averageDonation }}, 2000);
                        break;
                    case 'biggestDonation':
                        animateCounter('biggestDonation', 0,
                            {{ $biggestDonation ? $biggestDonation->amount_donate : 0 }}, 2000);
                        break;
                }
            }, 300);
            counterObserver.unobserve(entry.target);
        }
    });
}, {
    threshold: 0.5
});
            document.querySelectorAll('.stat-value').forEach(counter => {
                counterObserver.observe(counter);
            });
            const tableRows = document.querySelectorAll('.data-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', () => {
                    row.style.transition = 'background-color 0.3s ease';
                    row.style.backgroundColor = 'rgba(240, 85, 34, 0.03)';
                });

                row.addEventListener('mouseleave', () => {
                    row.style.backgroundColor = '';
                });
            });

            const exportBtn = document.querySelector('.export-btn');

            exportBtn.addEventListener('mouseenter', () => {
                exportBtn.style.transform = 'translateY(-2px)';
            });

            exportBtn.addEventListener('mouseleave', () => {
                exportBtn.style.transform = '';
            });

            exportBtn.addEventListener('click', function(e) {
                // Ripple effect
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                const x = e.clientX - e.target.getBoundingClientRect().left;
                const y = e.clientY - e.target.getBoundingClientRect().top;

                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;

                setTimeout(() => {
                    ripple.remove();
                }, 600);

                // Download the CSV file
                const downloadLink = document.createElement('a');
                downloadLink.href = "{{ route('admin.exportDonors') }}";
                downloadLink.setAttribute('download', 'donors.csv');
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);

                // Success notification
                const notification = document.createElement('div');
                notification.style.position = 'fixed';
                notification.style.bottom = '20px';
                notification.style.right = '20px';
                notification.style.backgroundColor = '#19345E';
                notification.style.color = '#ffffff';
                notification.style.padding = '15px 25px';
                notification.style.borderRadius = '8px';
                notification.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
                notification.style.display = 'flex';
                notification.style.alignItems = 'center';
                notification.style.zIndex = '1000';
                notification.style.transform = 'translateY(100px)';
                notification.style.transition = 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)';

                notification.innerHTML = `
        <i class="fas fa-check-circle" style="color: #F05522; margin-right: 10px; font-size: 1.2rem;"></i>
        <span>Report exported successfully!</span>
    `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.transform = 'translateY(0)';
                }, 100);

                setTimeout(() => {
                    notification.style.transform = 'translateY(100px)';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            });


            const headerParticles = document.createElement('div');
            headerParticles.classList.add('header-particles');
            document.querySelector('.dashboard-header').appendChild(headerParticles);

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.classList.add('particle');

                const size = Math.random() * 6 + 2;
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                const opacity = Math.random() * 0.3;
                const animDuration = Math.random() * 20 + 10;
                const animDelay = Math.random() * 10;

                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;
                particle.style.opacity = opacity;
                particle.style.animationDuration = `${animDuration}s`;
                particle.style.animationDelay = `${animDelay}s`;

                headerParticles.appendChild(particle);
            }

        });
    </script>
@endpush
