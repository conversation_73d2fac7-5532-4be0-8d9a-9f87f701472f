 import { createSlider } from '../appTwo.js';
document.addEventListener("DOMContentLoaded", function () {
    let debounceTimer;

    function calculateProgress(collected, goal) {
        if (!goal || goal === 0) return 0;
        const percentage = (collected / goal) * 100;
        return Math.min(percentage, 100);
    }

    function fetchFundraisers(url = "/leaderboard-data", search = "", sortBy = "total_collected") {

        const paginationLinks = document.getElementById("pagination-links");
                paginationLinks.innerHTML = "";

        const leaderboardContainer = document.getElementById("leaderboard-container");
        leaderboardContainer.innerHTML = `
    <div class="col-12">
        <div class="loader-container d-flex flex-column justify-content-center align-items-center" style="min-height: 300px;">
            <div class="spinner-border" role="status" style="color: #19345e; width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading Leaderboard...</p>
        </div>
    </div>
`;


        const fullUrl = new URL(url, window.location.origin);
        fullUrl.searchParams.set("search", search);
        fullUrl.searchParams.set("sortBy", sortBy);

        fetch(fullUrl)
            .then(response => response.json())
            .then(data => {
                leaderboardContainer.innerHTML = "";
                const paginationStatus=document.getElementById('pagination-status');
                const paginationLinks = document.getElementById("pagination-links");
                paginationLinks.innerHTML = "";
                paginationStatus.innerHTML="";
                if (data.data.length === 0) {
                    leaderboardContainer.innerHTML = `
                        <div class="col-12">
                            <div class="empty-state-container">
                                <div style="color: #19345e; font-size: 4rem; text-align: center;">
                                    <i class="fas fa-search"></i>
                                </div>
                                <h3 class="mt-3 text-center" style="color: #19345e;">
                                    ${search && search.value ? 'No fundraisers found<br>Try adjusting your search criteria' : 'There are no fundraisers'}
                                </h3>
                            </div>
                        </div>
                    `;
                    return;
                }

                const currentPage = data.current_page || 1;
                const perPage = data.per_page || 15;
                let startRank = (currentPage - 1) * perPage + 1;

                data.data.forEach((fundraiser, index) => {
                    const rank = startRank + index;
                    const progress = calculateProgress(fundraiser.total_collected || 0, fundraiser.fundraising_goal);

                    let imgSrc = fundraiser.profile_photo || "/uploads/default.png";
                    let cardDelay = index * 0.1;

                    let badgeClass = "";
                    if (rank === 1) badgeClass = "top-1";
                    else if (rank === 2) badgeClass = "top-2";
                    else if (rank === 3) badgeClass = "top-3";


                    const cardHtml = `
                        <div class="col-sm-6 col-lg-4 col-xl-3 mt-5" style="animation-delay: ${cardDelay}s;">
                            <a href="${fundraiser.donationPageUrl}" class="text-decoration-none w-100 h-100">
                                <div class="leader_box d-flex flex-column text-center align-items-center h-100">
                                    <div class="img mb-3">
                                        <img src="${imgSrc}" alt="${fundraiser.name}" />
                                    </div>
                                    <div class="text">
                                        <h2 class="mb-0">${fundraiser.name}</h2>
                                        <h3 class="price">
                                            $${(fundraiser.total_collected || 0).toLocaleString()} out of
                                            $${(fundraiser.fundraising_goal || 0).toLocaleString()}
                                        </h3>
                                    </div>
                                </div>
                            </a>
                        </div>
                    `;


                    // const cardHtml = `
                    //     <div class="col" style="animation-delay: ${cardDelay}s;">
                    //         <a href="${fundraiser.donationPageUrl}" class="text-decoration-none">
                    //             <div class="fundraiser-card">
                    //                 <div class="badge-rank ${badgeClass}">${rank}</div>
                    //                 <div class="profile-img-container">
                    //                     <img src="${imgSrc}" alt="${fundraiser.name}" class="profile-img">
                    //                 </div>
                    //                 <div class="card-body">
                    //                     <h3 class="fundraiser-name">${fundraiser.name}</h3>
                    //                     <p class="fundraiser-location">
                    //                         <i class="fas fa-map-marker-alt"></i> ${fundraiser.city || 'Location not specified'}
                    //                     </p>
                    //                     <div class="progress">
                    //                         <div class="progress-bar" role="progressbar" style="width: ${progress}%"
                    //                              aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100"></div>
                    //                     </div>
                    //                     <div class="amount-text mt-2">
                    //                         <span>$${(fundraiser.total_collected || 0).toLocaleString()}</span>
                    //                         <span>$${(fundraiser.fundraising_goal || 0).toLocaleString()}</span>
                    //                     </div>
                    //                 </div>
                    //             </div>
                    //         </a>
                    //     </div>
                    // `;

                    leaderboardContainer.insertAdjacentHTML('beforeend', cardHtml);
                });


                const shouldShowPagination = data.total > data.per_page && data.last_page > 1;

                // if (shouldShowPagination && data.links && data.links.length > 3) {
                //     let prevLink = document.createElement("li");
                //     prevLink.className = "page-item";
                //     prevLink.innerHTML = `
                //         <a class="page-link ${!data.prev_page_url ? 'disabled' : ''}" href="#" aria-label="Previous">
                //             <span aria-hidden="true"><i class="fas fa-chevron-left"></i></span>
                //         </a>
                //     `;
                //     if (data.prev_page_url) {
                //         prevLink.querySelector("a").addEventListener("click", function(e) {
                //             e.preventDefault();
                //             let searchQuery = document.getElementById("search").value;
                //             let selectedSort = document.getElementById("sort-filter").value;
                //             fetchFundraisers(data.prev_page_url, searchQuery, selectedSort);
                //         });
                //     }
                //     paginationLinks.appendChild(prevLink);

                //     data.links.forEach((link, index) => {

                //         if (index === 0 || index === data.links.length - 1) return;

                //         let pageLink = document.createElement("li");
                //         pageLink.className = "page-item";

                //         const label = link.label.replace(/<\/?[^>]+(>|$)/g, "");
                //         pageLink.innerHTML = `
                //             <a class="page-link ${link.active ? 'active' : ''} ${!link.url ? 'disabled' : ''}" href="#">
                //                 ${label}
                //             </a>
                //         `;

                //         if (link.url) {
                //             pageLink.querySelector("a").addEventListener("click", function(e) {
                //                 e.preventDefault();
                //                 let searchQuery = document.getElementById("search").value;
                //                 let selectedSort = document.getElementById("sort-filter").value;
                //                 fetchFundraisers(link.url, searchQuery, selectedSort);
                //                 window.scrollTo({top: 0, behavior: 'smooth'});
                //             });
                //         }

                //         paginationLinks.appendChild(pageLink);
                //     });

                //     let nextLink = document.createElement("li");
                //     nextLink.className = "page-item";
                //     nextLink.innerHTML = `
                //         <a class="page-link ${!data.next_page_url ? 'disabled' : ''}" href="#" aria-label="Next">
                //             <span aria-hidden="true"><i class="fas fa-chevron-right"></i></span>
                //         </a>
                //     `;
                //     if (data.next_page_url) {
                //         nextLink.querySelector("a").addEventListener("click", function(e) {
                //             e.preventDefault();
                //             let searchQuery = document.getElementById("search").value;
                //             let selectedSort = document.getElementById("sort-filter").value;
                //             fetchFundraisers(data.next_page_url, searchQuery, selectedSort);
                //         });
                //     }
                //     paginationLinks.appendChild(nextLink);
                // }

                    paginationLinks.innerHTML = "";
                    paginationStatus.innerHTML = "";

                    if (shouldShowPagination) {
                        const currentPage = data.current_page || 1;
                        const lastPage = data.last_page || 1;

                        if (data.prev_page_url) {
                            const prevBtn = document.createElement("button");
                            prevBtn.className = "cta orange me-2";
                            prevBtn.textContent = "PREVIOUS PAGE";
                            prevBtn.addEventListener("click", function (e) {
                                e.preventDefault();
                                let searchInput = document.getElementById("search");
                                let searchQuery = searchInput ? searchInput.value : "";
                                let sortFilter = document.getElementById("sort-filter");
                                let selectedSort = sortFilter ? sortFilter.value : "";
                                fetchFundraisers(data.prev_page_url, searchQuery, selectedSort);
                                window.scrollTo({ top: 0, behavior: "smooth" });
                            });
                            paginationLinks.appendChild(prevBtn);
                        }


                        if (data.next_page_url) {
                            const nextBtn = document.createElement("button");
                            nextBtn.className = "cta orange ms-2";
                            nextBtn.textContent = "NEXT PAGE";
                            nextBtn.addEventListener("click", function (e) {
                                e.preventDefault();
                                let searchInput = document.getElementById("search");
                                let searchQuery = searchInput ? searchInput.value : "";
                                let sortFilter = document.getElementById("sort-filter");
                                let selectedSort = sortFilter ? sortFilter.value : "";
                                fetchFundraisers(data.next_page_url, searchQuery, selectedSort);
                                window.scrollTo({ top: 0, behavior: "smooth" });
                            });
                            paginationLinks.appendChild(nextBtn);
                        }


                        paginationStatus.textContent = `${currentPage} of ${lastPage}`;
                    }


            })
            .catch(error => {
                console.error("Error fetching data:", error);
                const paginationLinks = document.getElementById("pagination-links");
                paginationLinks.innerHTML = "";
                leaderboardContainer.innerHTML = `
                    <div class="col-12 d-flex justify-content-center align-items-center" style="min-height: 300px;">
                        <div class="empty-state-container text-center">
                            <div style="color: #19345e; font-size: 4rem;">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <h3 class="mt-3">Something went wrong</h3>
                            <p class="text-muted">Unable to load fundraisers. Please try again later.</p>
                        </div>
                    </div>
                `;

            });
    }
    let search=document.getElementById('search');

      if(search){
       search.addEventListener("input", function () {
        const paginationLinks = document.getElementById("pagination-links");
                paginationLinks.innerHTML = "";
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            let selectedSort = document.getElementById("sort-filter").value;
            fetchFundraisers("/leaderboard-data", this.value, selectedSort);
        }, 300);
    });

    document.getElementById("sort-filter").addEventListener("change", function () {
        let searchQuery = document.getElementById("search").value;
        fetchFundraisers("/leaderboard-data", searchQuery, this.value);
    });

    document.getElementById("sort-filter").value = "total_collected";

    fetchFundraisers();
}
fetchFundraisers();
});
