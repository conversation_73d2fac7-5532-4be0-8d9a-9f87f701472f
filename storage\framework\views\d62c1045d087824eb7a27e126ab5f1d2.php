<?php $__env->startSection('title', 'Admin Dashboard'); ?>
<?php $__env->startSection('content'); ?>

    <?php echo $__env->make('layouts.admin-nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <section class="p-6">
        <h1 class="text-2xl font-bold mb-4">Subscriptions</h1>

        <?php if($subscriptions->count()): ?>
            <div class="overflow-x-auto rounded shadow">
                <table class="min-w-full border border-gray-200 bg-white text-sm text-left">
                    <thead class="bg-gray-100 text-gray-700 uppercase text-xs">
                        <tr>
                            <th class="px-4 py-3 border-b">Subscription ID</th>
                            <th class="px-4 py-3 border-b">Customer ID</th>
                            <th class="px-4 py-3 border-b">Frequency</th>
                            <th class="px-4 py-3 border-b">Amount</th>
                            <th class="px-4 py-3 border-b">Start Date</th>
                            <th class="px-4 py-3 border-b">Next Payment</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 border-b"><?php echo e($subscription->subscription_id); ?></td>
                                <td class="px-4 py-3 border-b"><?php echo e($subscription->customer_id); ?></td>
                                <td class="px-4 py-3 border-b capitalize"><?php echo e($subscription->payment_frequency); ?></td>
                                <td class="px-4 py-3 border-b">$<?php echo e(number_format($subscription->amount, 2)); ?></td>
                                <td class="px-4 py-3 border-b">
                                    <?php echo e(\Carbon\Carbon::parse($subscription->start_date)->format('M d, Y')); ?></td>
                                <td class="px-4 py-3 border-b">
                                    <?php echo e(\Carbon\Carbon::parse($subscription->next_payment_at)->diffForHumans()); ?></td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-gray-500 italic">No subscriptions found.</p>
        <?php endif; ?>
    </section>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\ASFL-Gauntlet\resources\views/Admin/subscriptions.blade.php ENDPATH**/ ?>