@extends('layouts.app')
@section('title', '2025 ASFL Gauntlet | Fundraiser Profile')
@section('content')

{{-- <section class="hero-section text-white" style="background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); padding: 80px 0;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-4 mb-4 mb-lg-0 text-center text-lg-start">
                <div class="profile-image-container" style="position: relative; display: inline-block;">
                    <img
                        src="{{Storage::url($user->profile_photo)}}"
                        class="rounded-circle img-fluid"
                        style="width: 200px; height: 200px; object-fit: cover; border: 6px solid white; box-shadow: 0 10px 30px rgba(0,0,0,0.2);"
                        alt="{{$user->name}}'s Profile"
                    >
                    <div class="pulse-animation" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border-radius: 50%; border: 3px solid rgba(255,255,255,0.7); animation: pulse 2s infinite;"></div>
                </div>
            </div>
            <div class="col-lg-8 text-center text-lg-start">
                <h1 class="display-4 mb-3 fw-bold" id="userName" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                    Support {{$user->name}}'s Fundraiser
                </h1>
                <div class="fundraiser-message mb-4" style="color: rgba(255,255,255,0.9); background: rgba(0,0,0,0.2); padding: 15px; border-radius: 10px; max-width: 100%;">
                    {!! $user->fund_raise_message !!}
                </div>


                <div class="progress-container">
                    @php

                        $percentage = ($totalAmount > 0) ? ($totalAmount / $user->fundraising_goal) * 100 : 0;
                        $displayWidth = min(100, $percentage);
                        $formattedPercentage = number_format($percentage, 1);
                    @endphp
                    <div class="progress" style="height: 30px; background-color: rgba(255,255,255,0.2); border-radius: 15px; overflow: hidden;">
                        <div
                            class="progress-bar"
                            role="progressbar"
                            style="width: {{$displayWidth}}%; background: #6a11cb; transition: width 1.5s ease-in-out;"
                            aria-valuenow="{{$percentage}}"
                            aria-valuemin="0"
                            aria-valuemax="100"
                        >
                            <span style="font-weight: bold; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                                ${{number_format($totalAmount, 0)}} ({{$formattedPercentage}}%)
                            </span>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mt-2" style="color: rgba(255,255,255,0.9);">
                        <div>${{number_format($totalAmount, 0)}} raised</div>
                        <div>Goal: ${{number_format($user->fundraising_goal, 0)}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Donation Options Section with Enhanced Visuals -->
<section class="donation-cards-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12 text-center mb-5">
                <h2 style="color: #6a11cb; font-weight: 700;">Choose Your Impact Level</h2>
                <p class="lead text-muted">Every donation makes a difference in the fight against cancer</p>
            </div>

            <div class="col-md-12">
                <div class="donation-cards-container" style="background-color: white; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1);">
                    <div class="donation-cards d-flex justify-content-between">
                        <div class="donation-card basic-card text-center p-4 rounded-3" data-amount="25">
                            <div class="card-icon mb-3">
                                <i class="fas fa-heart" style="font-size: 2.5rem; color: #6a11cb;"></i>
                            </div>
                            <h4 class="text-muted mb-3">Basic Donation</h4>
                            <div class="price mb-4">
                                <h2 style="color: #6a11cb;">$25</h2>
                            </div>
                            <span class="badge bg-light text-secondary">Supporter</span>
                        </div>

                        <div class="donation-card business-card text-center p-4 rounded-3" data-amount="100">
                            <div class="ribbon" style="position: absolute; top: 10px; right: -5px; background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%); color: white; padding: 5px 15px; font-size: 0.8rem; transform: rotate(45deg);">POPULAR</div>
                            <div class="card-icon mb-3">
                                <i class="fas fa-star" style="font-size: 2.5rem; color: #2575fc;"></i>
                            </div>
                            <h4 class="mb-3">Recommended</h4>
                            <div class="price mb-4">
                                <h2>$100</h2>
                            </div>
                            <span class="badge bg-primary">Champion</span>
                        </div>

                        <div class="donation-card enterprise-card text-center p-4 rounded-3" data-amount="250">
                            <div class="card-icon mb-3">
                                <i class="fas fa-medal" style="font-size: 2.5rem; color: #6a11cb;"></i>
                            </div>
                            <h4 class="text-muted mb-3">Supporter</h4>
                            <div class="price mb-4">
                                <h2 style="color: #6a11cb;">$250</h2>
                            </div>
                            <span class="badge bg-light text-secondary">Advocate</span>
                        </div>

                        <div class="donation-card impact-card text-center p-4 rounded-3" data-amount="500">
                            <div class="card-icon mb-3">
                                <i class="fas fa-trophy" style="font-size: 2.5rem; color: #6a11cb;"></i>
                            </div>
                            <h4 class="text-muted mb-3">Generous</h4>
                            <div class="price mb-4">
                                <h2 style="color: #6a11cb;">$500</h2>
                            </div>
                            <span class="badge bg-light text-secondary">Hero</span>
                        </div>

                        <div class="donation-card major-impact-card text-center p-4 rounded-3" data-amount="1000">
                            <div class="card-icon mb-3">
                                <i class="fas fa-crown" style="font-size: 2.5rem; color: #6a11cb;"></i>
                            </div>
                            <h4 class="text-muted mb-3">Major Contribution</h4>
                            <div class="price mb-4">
                                <h2 style="color: #6a11cb;">$1000</h2>
                            </div>
                            <span class="badge bg-light text-secondary">Guardian</span>
                        </div>
                    </div>
                </div>

                <!-- Custom Donation Form with Animation -->
                <form action="{{route('donor.details')}}" method="POST" id="customAmountForm">
                    @csrf
                    <div class="custom-donation-container">
                        <div class="donation-input-wrapper">
                            <h3 class="mb-4" style="color: #6a11cb;">Custom Donation</h3>
                            <div class="donation-input-group">
                                <div class="dollar-symbol">$</div>
                                <input
                                    type="number"
                                    name="amount"
                                    id="donationAmountInput"
                                    class="donation-amount-input"
                                    placeholder="0"
                                    min="1"
                                >
                                <input type="hidden" name="slug" value="{{$user->slug}}">
                            </div>
                            <div class="donation-input-underline"></div>
                            <div class="donation-description">
                                <p>Enter your custom donation amount</p>
                            </div>
                            <div class="donation-proceed-container">
                                <button id="proceedToDonationCustom" class="proceed-button" type="button">
                                    <i class="fas fa-heart mr-2"></i> Proceed to Donation
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Impact Section -->
<section class="impact-section py-5" style="background-color: #f0f4ff;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 text-center mb-5">
                <h2 style="color: #6a11cb; font-weight: 700;">Your Donation Makes a Difference</h2>
                <p class="lead text-muted">Here's how your contribution helps in the fight against cancer</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="impact-card h-100 p-4 text-center" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease;">
                    <div class="impact-icon mb-3">
                        <i class="fas fa-flask" style="font-size: 3rem; color: #6a11cb;"></i>
                    </div>
                    <h4 style="color: #2575fc;">Research</h4>
                    <p>Your donation helps fund vital cancer research to develop new treatments and therapies.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="impact-card h-100 p-4 text-center" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease;">
                    <div class="impact-icon mb-3">
                        <i class="fas fa-hand-holding-heart" style="font-size: 3rem; color: #6a11cb;"></i>
                    </div>
                    <h4 style="color: #2575fc;">Support</h4>
                    <p>Provides essential support services for patients and families throughout their cancer journey.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="impact-card h-100 p-4 text-center" style="background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: transform 0.3s ease;">
                    <div class="impact-icon mb-3">
                        <i class="fas fa-heartbeat" style="font-size: 3rem; color: #6a11cb;"></i>
                    </div>
                    <h4 style="color: #2575fc;">Prevention</h4>
                    <p>Supports education and awareness programs to help prevent cancer through early detection.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recent Donors Section with Enhanced Display -->
<section class="recent-donors py-5" style="background-color: #f8f9fa;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10 text-center mb-5">
                <h2 style="color: #6a11cb; font-weight: 700;">Join Our Community of Donors</h2>
                <p class="lead text-muted">These generous people have already made a difference</p>
            </div>
        </div>
    </div>
    @livewire('donors-in-donation-page', ['slug'=>$user->slug])
</section>
@endsection --}}

<section class="profile-hero">
    <div class="container">
        <div class="row align-items-center gx-xl-5">
            <div class="col-md-4">
                <div class="img-player"><img class="w-100" src="{{Storage::url($user->profile_photo)}}" alt="" /></div>
            </div>
            <div class="col-md-8">
                <div class="name">
                    <h1 class="text-uppercase">
                        Support<br />
                        <strong class="text-orange">{{$user->name}}'s</strong><br />
                        Fundraiser
                    </h1>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="achieve-goal">
    <div class="container-fluid gx-0">
        <div class="bg-blue">
            <div class="container">
                <div class="meta-timeline d-flex justify-content-between">
                    <div class="meta text-center">
                        <h2>
                            Raised:<br />
                            <strong>${{number_format($totalAmount, 0)}}</strong>
                        </h2>
                    </div>
                    <div class="meta text-center">
                        <h2>
                            Goal:<br />
                            <strong>${{number_format($user->fundraising_goal, 0)}}</strong>
                        </h2>
                    </div>
                </div>
                <div class="time-line mt-3">
                    <div class="bar">
                        @if($totalAmount > 0)
                          <span class="fill align-items-center d-flex"
                                style="width: {{ min(($totalAmount / $user->fundraising_goal) * 100, 100) }}%">
                          </span>
                        @endif
                      </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="sec your-donation">
			<div class="container">
				<div class="head text-center">
					<h2 class="text-uppercase">Message For Donors</h2>
					<h3 class="mb-0">{!! $user->fund_raise_message !!} </h3>
				</div>

			</div>
		</section>




<section class="sec donate-wrap select-level">
    <div class="container">
        <div class="head text-center mb-5">
            <h2 class="text-uppercase">Choose your impact level</h2>
            <h3>Every donation makes a difference in the fight against cancer!</h3>
        </div>
        <div class="select-package row donation-cards-container">
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4 donation-cards">
                <div class="check donation-card" data-amount="25">
                    <input type="radio" name="package" value="supporter" id="supporter" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="supporter">$25 <span>Supporter</span> <span class="tag text-nowrap">Basic Donation</span></label>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check star donation-card" data-amount="200">
                    <input type="radio" name="package" value="champion" id="champion" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="champion">$200 <span>Champion</span> <span class="tag text-nowrap">Recommended</span></label>
                    <div class="most-selected d-flex align-items-center flex-column">
                        <div class="icon"><i class="bi bi-star-fill"></i></div>
                        <h2 class="text-uppercase">Popular Choice</h2>
                    </div>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check donation-card" data-amount="250">
                    <input type="radio" name="package" value="advocate" id="advocate" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="advocate">$250 <span>Advocate</span> <span class="tag text-nowrap">Supporter</span></label>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check donation-card" data-amount="500">
                    <input type="radio" name="package" value="hero" id="hero" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="hero">$500 <span>Hero</span> <span class="tag text-nowrap">Generous</span></label>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-3 col-xl mb-4">
                <div class="check donation-card" data-amount="1000">
                    <input type="radio" name="package" value="guardian" id="guardian" />
                    <label class="d-flex align-items-center justify-content-center flex-column p-3" for="guardian">$1,000 <span>Guardian</span> <span class="tag text-nowrap">Major Contribution</span></label>
                </div>
            </div>
        </div>

        <form action="{{route('donor.details')}}" method="POST" id="customAmountForm">
            @csrf
        <div class="text-center mb-5">
            <h2 class="mb-3">Or Enter Your Own Amount</h2>
            <div class="input-price mx-auto mb-4">
                <label class="visually-hidden" for="amount">Amount</label>
                <div class="input-group">
                    <div class="input-group-text">$</div>
                    <input class="form-control donation-amount-input" type="number"  name="amount"  id="donationAmountInput" inputmode="numeric" />
                </div>
            </div>
            <input type="hidden" name="slug" value="{{$user->slug}}">
            <div class="text-center mb-5 donation-proceed-container">
                <button id="proceedToDonationCustom" class="cta orange border-0">Continue</button>
            </div>
        </div>
    </form>
    </div>
</section>
<section class="sec your-donation">
    <div class="container">
        <div class="head text-center mb-5">
            <h2 class="text-uppercase">Your Donation Makes a Difference</h2>
            <h3>Here’s how your contribution helps in the fight against cancer!</h3>
        </div>
        <div class="row">
            <div class="col-md-4 mt-5">
                <div class="box text-center">
                    <div class="icon mb-4"><img src={{asset('images/research-icon.svg')}} alt="" width="116" height="116" /></div>
                    <div class="text">
                        <h2>Research</h2>
                        <p>Your donation helps fund vital cancer research to develop new treatments and therapies.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mt-5">
                <div class="box text-center">
                    <div class="icon mb-4"><img src={{asset('images/support-icon.svg')}} alt="" width="116" height="116" /></div>
                    <div class="text">
                        <h2>Support</h2>
                        <p>Provides essential support services for patients and families throughout their cancer journey.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mt-5">
                <div class="box text-center">
                    <div class="icon mb-4"><img src={{asset('images/prevention-icon.svg')}} alt="" width="116" height="116" /></div>
                    <div class="text">
                        <h2>Prevention</h2>
                        <p>Supports education and awareness programs to help prevent cancer through early detection.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@livewire('donors-in-donation-page', ['slug'=>$user->slug])

@endsection

@push('styles')
<style>



    /* Donation Cards Styling */
    .donation-cards-container {
        padding: 30px;
        transition: all 0.3s ease;
    }


    .donation-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .donation-card:hover {
        transform: translateY(-10px);
    }


    .donation-input-wrapper:hover {
        box-shadow: 0 20px 40px rgba(106, 17, 203, 0.15);
    }

    .donation-input-group {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }

    .donation-amount-input::placeholder {
        color: #a0a0a0;
    }

    .donation-amount-input:focus + .donation-input-underline {
        height: 5px;
        box-shadow: 0 2px 10px rgba(106, 17, 203, 0.3);
    }

    .donation-description {
        margin-bottom: 30px;
        color: #6c757d;
    }

    .proceed-button:active {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(106, 17, 203, 0.2);
    }
    .cta.orange {

width: 47px !important;
height: 33px;
display: inline-flex;
align-items: center;
justify-content: center;
transition: opacity 0.3s ease;
border: none;
}

.cta-button.clicked {
transform: scale(0.95);
opacity: 0.8;
transition: transform 0.2s ease, opacity 0.2s ease;
}


.cta.orange.disabled {
opacity: 0.5;
cursor: not-allowed;
width: 47px !important;
height: 33px;
display: inline-flex;
align-items: center;
justify-content: center;
transition: opacity 0.3s ease;
border: none;
}

.activities-container {
transition: opacity 0.3s ease;
min-height: 300px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {

    const donationCards = document.querySelectorAll('.donation-card');
    const customAmountInput = document.getElementById('donationAmountInput');
    const customProceedButton = document.getElementById('proceedToDonationCustom');
    let selectedAmount = null;

    customProceedButton.innerHTML="Continue";
    customProceedButton.disabled = false;

    donationCards.forEach(card => {
    card.addEventListener('click', function(e) {
        // Prevent the event from being handled if the radio button itself is clicked
        if (e.target.tagName === 'INPUT') return;




        donationCards.forEach(c => c.classList.remove('selected'));
        this.classList.add('selected');

        selectedAmount = this.getAttribute('data-amount');
        customAmountInput.value = selectedAmount;
    }, { once: false });
});


    // const impactCards = document.querySelectorAll('.impact-card');
    // if (impactCards.length > 0) {
    //     impactCards.forEach(card => {
    //         card.addEventListener('mouseenter', function() {
    //             this.style.transform = 'translateY(-10px)';
    //             this.style.boxShadow = '0 15px 30px rgba(106, 17, 203, 0.15)';
    //         });

    //         card.addEventListener('mouseleave', function() {
    //             this.style.transform = 'translateY(0)';
    //             this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
    //         });
    //     });
    // }


    customProceedButton.addEventListener('click', function(e) {
        e.preventDefault();

        let form = document.getElementById('customAmountForm');
        selectedAmount = customAmountInput.value;

        if (!selectedAmount || selectedAmount <= 0) {

            customAmountInput.style.border = '2px solid #ff5252';
            customAmountInput.style.backgroundColor = 'rgba(255,82,82,0.1)';


            if (!document.querySelector('.error-message')) {
                const errorMessage = document.createElement('div');
                errorMessage.classList.add('error-message');
                errorMessage.style.color = '#ff5252';
                errorMessage.style.marginTop = '10px';
                errorMessage.innerHTML = 'Please enter a valid donation amount';
                document.querySelector('.donation-description').appendChild(errorMessage);


                setTimeout(() => {
                    customAmountInput.style.border = 'none';
                    customAmountInput.style.backgroundColor = 'transparent';
                    if (document.querySelector('.error-message')) {
                        document.querySelector('.error-message').remove();
                    }
                }, 3000);
            }
        } else {



            customProceedButton.innerHTML = '<i class="spinner-border spinner-border-sm text-white" role="status"></i>';
            customProceedButton.disabled = true;


            setTimeout(() => {
                form.submit();

            }, 800);
        }

    });


    customAmountInput.addEventListener('input', function() {
        this.style.border = 'none';
        this.style.backgroundColor = 'transparent';
        if (document.querySelector('.error-message')) {
            document.querySelector('.error-message').remove();
        }
    });
});
</script>
@endpush
